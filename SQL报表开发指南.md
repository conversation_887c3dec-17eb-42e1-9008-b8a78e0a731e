# SQL报表开发指南

## 概述

本指南基于康方报表管理系统的成本控制模块，为新接手的同事提供SQL报表开发的规范和最佳实践。系统基于RuoYi框架，使用SQL Server数据库，主要处理费用申请、报销、预算控制等业务场景。

## 核心表结构

### 1. 费用管理核心表
- `exp_claim_header`: 费用单据头表，存储单据基本信息
- `exp_claim_line`: 费用单据行表，存储明细数据
- `exp_header_type`: 单据类型定义表
- `exp_budget_*`: 预算相关表系列

### 2. 值列表系统（LOV）
- `fnd_lov`: 值列表类型定义表
- `fnd_lov_value`: 值列表值表  
- `fnd_lov_value_tl`: 值列表翻译表（多语言支持）

### 3. 组织架构表
- `fnd_company`: 公司信息
- `fnd_department`: 部门信息
- `fnd_position`: 职位信息
- `fnd_user`: 用户信息

## 关键开发原则

### 1. 数据关联的准确性："所见"不一定是"所得"

**核心问题**: 业务表中存储的往往是代码而非显示名称，需要通过值列表系统进行转换。

```sql
-- 错误示例：直接从业务表取值
SELECT h.column46 as 项目号
FROM exp_claim_header h;

-- 正确示例：通过LOV系统转换
SELECT 
    COALESCE(proj_lov.value_meaning, h.column46) as 项目号
FROM exp_claim_header h
LEFT JOIN fnd_lov proj_type ON proj_type.lov_name = 'Project_number'
LEFT JOIN fnd_lov_value proj_val ON proj_val.lov_id = proj_type.lov_id 
    AND proj_val.value_code = h.column46
LEFT JOIN fnd_lov_value_tl proj_lov ON proj_lov.value_id = proj_val.value_id 
    AND proj_lov.language = 'zh_CN';
```

### 2. 自定义字段的发现与利用

系统大量使用 `columnXX` 字段存储业务数据：

**常用字段映射关系**：
- `h.column46`: 项目号代码
- `l.column42`: 终端代码  
- `dept.column2`: 所属片区代码
- `h.column15`: IIT分类代码

### 3. JOIN策略选择

**LEFT JOIN vs INNER JOIN 的选择原则**：

```sql
-- 使用LEFT JOIN保证主表数据完整性
SELECT 
    h.document_num,
    CASE 
        WHEN budget.status = 'APPROVED' THEN '已占用'
        WHEN budget.status = 'CONSUMED' THEN '已消耗'
        ELSE NULL
    END as 预算状态
FROM exp_claim_header h
LEFT JOIN exp_budget_status budget ON budget.header_id = h.header_id;

-- 只有在确保关联关系对所有记录都成立时才使用INNER JOIN
SELECT h.document_num, ht.type_name
FROM exp_claim_header h
INNER JOIN exp_header_type ht ON ht.type_id = h.header_type_id;
```

## 单据类型与代码映射

### 主要单据类型

| 类型名称 | 类型代码 | 描述 |
|---------|---------|------|
| FSAA-差旅申请 | YXCL01 | 差旅费用申请单据 |
| FSAB-差旅报销 | YXCL02 | 差旅费用报销单据 |
| FSBA-费用申请 | YXFY01 | 一般费用申请单据 |
| FSBB-费用报销 | YXFY02 | 一般费用报销单据 |
| FSEA-IIT立项申请 | YXIIT01 | IIT项目立项申请 |
| FSHB-对公预付（含会议） | YXDG01 | 对公预付款单据 |
| FSHC-对公应付（除会议外） | YXDG02 | 对公应付款单据 |

### 单据状态映射

```sql
CASE h.status
    WHEN 'SAVED' THEN '已保存'
    WHEN 'SUBMITTED' THEN '已提交'  
    WHEN 'APPROVED' THEN '已审批'
    WHEN 'REJECTED' THEN '已拒绝'
    WHEN 'CANCELLED' THEN '已取消'
    ELSE h.status
END as 单据状态
```

## 值列表查询模式

### 标准LOV查询模板

```sql
-- 标准三表关联查询模式
SELECT 
    业务字段,
    COALESCE(lov_tl.value_meaning, 业务表.原始字段) as 显示名称
FROM 业务表
LEFT JOIN fnd_lov lov ON lov.lov_name = '值列表类型代码'
LEFT JOIN fnd_lov_value lov_val ON lov_val.lov_id = lov.lov_id 
    AND lov_val.value_code = 业务表.原始字段
LEFT JOIN fnd_lov_value_tl lov_tl ON lov_tl.value_id = lov_val.value_id 
    AND lov_tl.language = 'zh_CN';
```

### 常用值列表类型

| 值列表代码 | 用途 | 说明 |
|-----------|------|------|
| Project_number | 项目号 | 项目编号转换 |
| categories | IIT分类 | IIT项目分类 |
| CPGX | 产品管线 | 产品线分类 |
| CBZXDX | 部门所属片区 | 组织架构片区 |
| Terminal_level | 终端级别 | 医院终端分级 |
| Personal_type | 费用类别 | 费用分类 |

## 报表查询最佳实践

### 1. 基础报表结构

```sql
WITH base_data AS (
    -- 基础数据查询
    SELECT 
        h.header_id,
        h.document_num as 单据号,
        h.creation_date as 创建日期,
        h.submit_date as 提交日期,
        h.status as 原始状态,
        h.column46 as 项目号代码,
        -- 其他基础字段
    FROM exp_claim_header h
    WHERE h.company_id = 1
        AND h.creation_date >= '2024-01-01'
),
enriched_data AS (
    -- 数据增强（关联LOV等）
    SELECT 
        bd.*,
        CASE bd.原始状态
            WHEN 'SAVED' THEN '已保存'
            WHEN 'SUBMITTED' THEN '已提交'
            ELSE bd.原始状态
        END as 单据状态,
        COALESCE(proj_lov.value_meaning, bd.项目号代码) as 项目号
    FROM base_data bd
    LEFT JOIN fnd_lov proj_type ON proj_type.lov_name = 'Project_number'
    LEFT JOIN fnd_lov_value proj_val ON proj_val.lov_id = proj_type.lov_id 
        AND proj_val.value_code = bd.项目号代码
    LEFT JOIN fnd_lov_value_tl proj_lov ON proj_lov.value_id = proj_val.value_id 
        AND proj_lov.language = 'zh_CN'
)
-- 最终查询
SELECT * FROM enriched_data
ORDER BY 创建日期 DESC;
```

### 2. 性能优化建议

```sql
-- 1. 使用适当的索引提示
SELECT /*+ INDEX(h, exp_claim_header_n1) */
    h.document_num
FROM exp_claim_header h
WHERE h.company_id = 1 AND h.status = 'APPROVED';

-- 2. 避免在大表上使用函数
-- 错误
WHERE YEAR(h.creation_date) = 2024

-- 正确  
WHERE h.creation_date >= '2024-01-01' 
    AND h.creation_date < '2025-01-01'

-- 3. 合理使用EXISTS替代IN
SELECT h.document_num
FROM exp_claim_header h
WHERE EXISTS (
    SELECT 1 FROM exp_claim_line l 
    WHERE l.header_id = h.header_id 
        AND l.amount > 1000
);
```

### 3. 分页查询模式

```sql
-- 使用OFFSET-FETCH进行分页（SQL Server 2012+）
SELECT 
    document_num,
    creation_date,
    status
FROM exp_claim_header
WHERE company_id = 1
ORDER BY creation_date DESC
OFFSET (@page - 1) * @pageSize ROWS
FETCH NEXT @pageSize ROWS ONLY;
```

## 预算相关查询

### 预算状态判断逻辑

```sql
SELECT 
    h.document_num,
    CASE 
        WHEN ht.type_code LIKE '%01' THEN '预算占用'  -- 申请类单据
        WHEN ht.type_code LIKE '%02' THEN '预算消耗'  -- 报销类单据
        ELSE '无预算影响'
    END as 预算状态
FROM exp_claim_header h
LEFT JOIN exp_header_type ht ON ht.type_id = h.header_type_id;
```

## 常见问题与解决方案

### 1. 字段取值为空的问题

**原因**: 
- 数据存储在非标准字段中
- 需要通过LOV转换
- 关联条件不正确

**解决方案**:
```sql
-- 使用COALESCE提供默认值
SELECT COALESCE(lov_value, original_field, '未知') as 显示字段

-- 检查数据存储位置
SELECT column1, column2, column3, ..., column50
FROM exp_claim_header 
WHERE header_id = 具体ID;
```

### 2. 关联查询结果为空

**原因**: 
- 使用了INNER JOIN但关联表无数据
- 关联条件过于严格

**解决方案**:
```sql
-- 改用LEFT JOIN并检查关联条件
SELECT h.*, dept.dept_name
FROM exp_claim_header h
LEFT JOIN fnd_department dept ON dept.dept_id = h.department_id
    AND dept.enabled_flag = 'Y';  -- 注意启用状态
```

### 3. 性能问题

**常见原因**:
- 缺少合适索引
- 查询范围过大
- 不必要的函数调用

**解决方案**:
```sql
-- 添加时间范围限制
WHERE h.creation_date >= DATEADD(month, -3, GETDATE())

-- 使用EXISTS替代复杂子查询
WHERE EXISTS (SELECT 1 FROM related_table r WHERE r.id = h.id)
```

## 开发规范

### 1. 命名规范
- 表别名使用有意义的缩写：`h`(header), `l`(line), `dept`(department)
- 字段别名使用中文业务名称
- 临时表使用temp_前缀

### 2. 代码格式
```sql
SELECT 
    h.document_num as 单据号,
    h.creation_date as 创建日期,
    CASE h.status
        WHEN 'APPROVED' THEN '已审批'
        ELSE h.status  
    END as 单据状态
FROM exp_claim_header h
LEFT JOIN exp_header_type ht ON ht.type_id = h.header_type_id
WHERE h.company_id = 1
    AND h.creation_date >= '2024-01-01'
ORDER BY h.creation_date DESC;
```

### 3. 测试建议
- 先用小数据集验证逻辑正确性
- 检查边界条件（NULL值、空字符串）
- 验证关联关系的完整性
- 测试不同时间范围的数据

## 工具与资源

### 1. 开发工具
- SQL Server Management Studio (SSMS)
- 数据库表结构文档
- 值列表知识图谱（参考doc目录）

### 2. 参考文档
- 单据类型映射表
- 字段映射关系表  
- 值列表完整清单

### 3. 调试技巧
```sql
-- 查看执行计划
SET SHOWPLAN_ALL ON
SELECT ...

-- 检查表结构
sp_help 'exp_claim_header'

-- 查看索引使用情况  
SELECT * FROM sys.dm_db_index_usage_stats
WHERE object_id = OBJECT_ID('exp_claim_header')
```

## 总结

开发报表时要记住：
1. **理解业务逻辑胜过复杂技术实现**
2. **优先使用LEFT JOIN保证数据完整性**  
3. **善于发现并利用自定义字段columnXX**
4. **通过LOV系统转换代码为名称**
5. **注重查询性能和用户体验**

遇到问题时，回到业务本质思考，往往能找到更简单有效的解决方案。