# IIT赠药报表单据状态功能使用说明

## 功能概述

本次更新为IIT赠药报表增加了完整的单据状态管理功能，包括：
- 单据状态字段的查询和显示
- 状态代码到中文名称的自动转换
- 前端状态过滤功能（支持单选和多选）
- Excel导出中包含状态信息

## 技术实现

### 1. 后端实现

#### 1.1 数据库层面
- **SQL查询增强**: 在`IITDonationReportMapper.xml`中增加了状态字段查询
- **状态转换**: 使用CASE语句将状态代码转换为中文显示
- **动态过滤**: 支持单个状态和多个状态的过滤条件

#### 1.2 Java代码层面
- **枚举类**: `DocumentStatusEnum` - 定义所有可用的单据状态
- **DTO增强**: `IITDonationReportDTO` - 增加状态相关字段
- **查询参数**: `IITDonationReportQuery` - 支持状态列表过滤
- **Controller增强**: 增加参数验证和状态选项接口

### 2. 前端配置

#### 2.1 状态选项配置
```javascript
const DOCUMENT_STATUS_CONFIG = {
    options: [
        { code: 'DRAFT', name: '草稿' },
        { code: 'SUBMIT', name: '已提交' },
        { code: 'APPROVE', name: '已审批' },
        { code: 'REJECT', name: '已驳回' },
        { code: 'CANCEL', name: '已取消' },
        { code: 'CLOSE', name: '已关闭' }
    ]
};
```

#### 2.2 颜色映射
- 草稿: 灰色 (#909399)
- 已提交: 蓝色 (#409EFF)
- 已审批: 绿色 (#67C23A)
- 已驳回: 红色 (#F56C6C)
- 已取消: 橙色 (#E6A23C)
- 已关闭: 灰色 (#909399)

## API接口说明

### 1. 分页查询接口
**URL**: `GET /marketingFinance/iitDonationReport/page`

**请求参数**:
```json
{
    "pageNum": 1,
    "pageSize": 10,
    "projectNumber": "项目号",
    "iitClass": "IIT分类",
    "setupDepartment": "立项部门",
    "chargeDepartment": "费用承担部门",
    "iitCenter": "IIT中心",
    "statusList": ["DRAFT", "SUBMIT"],  // 多选状态
    "status": "DRAFT"                   // 单选状态（兼容性保留）
}
```

### 2. 导出接口
**URL**: `POST /marketingFinance/iitDonationReport/export`

**请求参数**: 同分页查询接口

### 3. 状态选项接口
**URL**: `GET /marketingFinance/iitDonationReport/statusOptions`

**响应数据**:
```json
{
    "code": 200,
    "data": [
        {"code": "DRAFT", "name": "草稿"},
        {"code": "SUBMIT", "name": "已提交"},
        {"code": "APPROVE", "name": "已审批"},
        {"code": "REJECT", "name": "已驳回"},
        {"code": "CANCEL", "name": "已取消"},
        {"code": "CLOSE", "name": "已关闭"}
    ]
}
```

## 使用方法

### 1. 前端页面集成

#### 1.1 引入配置文件
```html
<script src="/js/costControl/iitDonationReport.js"></script>
```

#### 1.2 状态选择组件
```html
<!-- 多选状态 -->
<el-select v-model="queryForm.statusList" multiple placeholder="请选择单据状态">
    <el-option
        v-for="option in statusOptions"
        :key="option.code"
        :label="option.name"
        :value="option.code">
    </el-option>
</el-select>
```

#### 1.3 表格状态列显示
```html
<el-table-column prop="statusName" label="单据状态" width="100">
    <template slot-scope="scope">
        <span :style="{color: getStatusColor(scope.row.status), fontWeight: 'bold'}">
            {{ scope.row.statusName }}
        </span>
    </template>
</el-table-column>
```

### 2. 后端服务调用

#### 2.1 Service层调用
```java
@Autowired
private IITDonationReportService iitDonationReportService;

// 构建查询条件
IITDonationReportQuery query = new IITDonationReportQuery();
query.setStatusList(Arrays.asList("DRAFT", "SUBMIT"));
query.setProjectNumber("PRJ001");

// 分页查询
Page<IITDonationReportDTO> result = iitDonationReportService.page(query);

// 列表查询（用于导出）
List<IITDonationReportDTO> list = iitDonationReportService.list(query);
```

## 数据字段说明

### 1. 新增字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| status | String | 原始状态代码 | DRAFT |
| statusName | String | 状态中文名称 | 草稿 |

### 2. Excel导出字段
导出的Excel文件将包含"单据状态"列，显示中文状态名称。

## 注意事项

### 1. 兼容性
- 保留了原有的单个状态过滤参数`status`，确保向后兼容
- 新增的多选状态参数`statusList`优先级更高

### 2. 参数验证
- 状态代码必须是预定义的有效值
- 无效的状态代码会返回参数错误

### 3. 性能考虑
- 状态过滤使用索引，查询性能良好
- 建议在`exp_claim_header.status`字段上建立索引

### 4. 扩展性
- 新增状态只需在`DocumentStatusEnum`中添加即可
- 前端配置会自动同步更新

## 测试验证

### 1. 功能测试
- 执行`src/test/resources/sql/iit_donation_report_test.sql`中的测试SQL
- 验证状态转换和过滤功能是否正常

### 2. 接口测试
- 使用Postman或其他工具测试API接口
- 验证参数验证和异常处理是否正常

### 3. 前端测试
- 验证状态选择组件是否正常工作
- 验证表格状态列显示是否正确
- 验证Excel导出是否包含状态信息

## 常见问题

### Q1: 状态显示为代码而不是中文名称
**A**: 检查SQL中的CASE语句是否正确，确保状态转换逻辑正常。

### Q2: 状态过滤不生效
**A**: 检查查询参数是否正确传递，确保statusList或status参数有值。

### Q3: 新增状态后前端不显示
**A**: 需要同时更新`DocumentStatusEnum`和前端配置文件`iitDonationReport.js`。

## 更新日志

- **2025/8/1**: 初始版本，增加单据状态查询、过滤和显示功能
- 支持多选状态过滤
- 增加状态中文名称转换
- 完善参数验证和异常处理
