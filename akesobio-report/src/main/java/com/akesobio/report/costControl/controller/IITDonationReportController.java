package com.akesobio.report.costControl.controller;

import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.utils.poi.EasyExcelUtils;
import com.akesobio.report.costControl.mapper.query.IITDonationReportQuery;
import com.akesobio.report.costControl.service.IITDonationReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 29、IIT赠药报表
 *
 * <AUTHOR>
 * @since 2025/6/25  16:13
 */
@RestController
@RequestMapping("/marketingFinance/iitDonationReport")
public class IITDonationReportController extends BaseController {
    @Resource
    IITDonationReportService iitDonationReportService;

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermi('marketingFinance:IITDonationReport:page')")
    public AjaxResult page(IITDonationReportQuery query){
        startPage();
        return AjaxResult.success(getDataTable(iitDonationReportService.page(query).getRecords()));
    }

    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('marketingFinance:IITDonationReport:export')")
    public void export(HttpServletResponse response, IITDonationReportQuery query) throws IOException {
        EasyExcelUtils.download(response, iitDonationReportService.list(query), "IIT赠药报表");
    }
}
