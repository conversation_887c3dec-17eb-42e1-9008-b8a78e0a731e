# 字段映射说明文档

## 概述

本文档详细说明了康方报表管理系统成本控制模块中各表的关键字段映射关系，特别是自定义字段（column1-column50）的业务含义和使用方法。

## 1. 核心业务表字段映射

### 1.1 exp_claim_header 字段映射

#### 标准字段
| 字段名 | 数据类型 | 业务含义 | 备注 |
|--------|----------|----------|------|
| header_id | int | 单据头ID | 主键，自增 |
| company_id | int | 公司ID | 外键，关联公司表 |
| document_num | varchar(64) | 单据编号 | 业务唯一编号 |
| internal_type | varchar(64) | 单据内部类型 | 关联LOV: receipts_type |
| status | varchar(16) | 单据状态 | DRAFT/SUBMIT/APPROVE/REJECT等 |
| submit_user | int | 提交人ID | 外键，关联fnd_user |
| charge_user | int | 承担人ID | 外键，关联fnd_user |
| submit_department | int | 提交部门ID | 外键，关联fnd_department |
| charge_department | int | 承担部门ID | 外键，关联fnd_department |
| total_amount | decimal(65,2) | 总金额 | 单据总金额 |
| submit_date | datetime | 提交日期 | 单据提交时间 |
| creation_date | datetime | 创建日期 | 记录创建时间 |

#### 自定义字段映射
| 字段名 | 业务含义 | 关联LOV类型 | 使用说明 |
|--------|----------|-------------|----------|
| column1 | 预留字段1 | - | 根据业务需要使用 |
| column2 | 预留字段2 | - | 根据业务需要使用 |
| column15 | IIT分类 | categories | 研究者发起IIT/公司发起IIT等 |
| column46 | 项目号 | Project_number | 项目编号，关联项目主数据 |

### 1.2 exp_claim_line 字段映射

#### 标准字段
| 字段名 | 数据类型 | 业务含义 | 备注 |
|--------|----------|----------|------|
| line_id | int | 行ID | 主键，自增 |
| header_id | int | 单据头ID | 外键，关联exp_claim_header |
| company_id | int | 公司ID | 外键，关联公司表 |
| line_type | varchar(64) | 行类型 | 费用类型 |
| amount | decimal(65,2) | 金额 | 行金额 |
| description | varchar(255) | 描述 | 费用描述 |
| cost_center_id | int | 成本中心ID | 成本中心 |

#### 自定义字段映射
| 字段名 | 业务含义 | 关联LOV类型 | 使用说明 |
|--------|----------|-------------|----------|
| column1 | 预留字段1 | - | 根据业务需要使用 |
| column42 | 终端代码 | ZD01 | 终端标识，关联终端主数据 |

## 2. 组织架构表字段映射

### 2.1 fnd_department 字段映射

#### 标准字段
| 字段名 | 数据类型 | 业务含义 | 备注 |
|--------|----------|----------|------|
| department_id | int | 部门ID | 主键，自增 |
| company_id | int | 公司ID | 外键 |
| supervisor_id | int | 上级部门ID | 外键，自关联形成层级 |
| department_code | varchar(64) | 部门代码 | 部门唯一编码 |
| natural_level | int | 自然层级 | 部门在组织架构中的层级 |
| enabled_flag | varchar(1) | 启用标志 | Y/N |

#### 自定义字段映射
| 字段名 | 业务含义 | 关联LOV类型 | 使用说明 |
|--------|----------|-------------|----------|
| column1 | 预留字段1 | - | 根据业务需要使用 |
| column2 | 所属片区 | CBZXDX | 部门所属片区标识 |

### 2.2 fnd_user 字段映射

#### 标准字段
| 字段名 | 数据类型 | 业务含义 | 备注 |
|--------|----------|----------|------|
| user_id | int | 用户ID | 主键，自增 |
| company_id | int | 公司ID | 外键 |
| user_name | varchar(64) | 用户名 | 登录用户名，唯一 |
| full_name | varchar(255) | 全名 | 用户显示名称 |
| department_id | int | 部门ID | 外键，关联fnd_department |
| employee_number | varchar(64) | 员工号 | 员工编号 |
| enabled_flag | varchar(1) | 启用标志 | Y/N |

## 3. 值列表系统字段映射

### 3.1 fnd_lov 字段映射

| 字段名 | 数据类型 | 业务含义 | 备注 |
|--------|----------|----------|------|
| lov_id | int | LOV类型ID | 主键，自增 |
| company_id | int | 公司ID | 外键 |
| lov_name | varchar(64) | LOV类型名称 | 唯一，如categories、Project_number |
| enabled_flag | varchar(1) | 启用标志 | Y/N |
| department_control | varchar(1) | 部门管控 | 是否按部门权限控制 |
| user_control | varchar(1) | 人员管控 | 是否按人员权限控制 |

### 3.2 fnd_lov_value 字段映射

| 字段名 | 数据类型 | 业务含义 | 备注 |
|--------|----------|----------|------|
| value_id | int | 值ID | 主键，自增 |
| lov_id | int | LOV类型ID | 外键，关联fnd_lov |
| company_id | int | 公司ID | 外键 |
| value_code | varchar(64) | 值代码 | 业务表引用的代码值 |
| enabled_flag | varchar(1) | 启用标志 | Y/N |

### 3.3 fnd_lov_value_tl 字段映射

| 字段名 | 数据类型 | 业务含义 | 备注 |
|--------|----------|----------|------|
| value_id | int | 值ID | 主键，外键，关联fnd_lov_value |
| language | varchar(16) | 语言 | 主键，zh_CN/en_US等 |
| value_meaning | varchar(255) | 显示名称 | 对应语言的显示值 |
| description | text | 描述 | 详细描述 |

## 4. 关键LOV类型及其含义

基于文档分析，系统中定义了47种LOV类型，以下是主要类型的说明：

### 4.1 业务分类相关
| LOV类型名称 | 中文名称 | 业务用途 | 关联字段 |
|-------------|----------|----------|----------|
| categories | IIT分类 | 区分研究者发起IIT/公司发起IIT | exp_claim_header.column15 |
| receipts_type | 单据类型 | 单据类型分类 | exp_claim_header.internal_type |
| Project_number | 项目号 | 项目编号管理 | exp_claim_header.column46 |
| ZD01 | 终端 | 终端管理 | exp_claim_line.column42 |
| CBZXDX | 部门所属片区 | 组织架构片区管理 | fnd_department.column2 |

### 4.2 费用管理相关
| LOV类型名称 | 中文名称 | 业务用途 |
|-------------|----------|----------|
| Personal_type | 费用类别 | 个人费用分类 |
| YW01 | 费用类型 | 业务费用类型 |
| YSKM | 预算科目 | 预算管理科目 |
| City Class | 城市分类 | 差旅城市等级 |
| Flight Class | 飞机舱位 | 差旅舱位标准 |

### 4.3 审批流程相关
| LOV类型名称 | 中文名称 | 业务用途 |
|-------------|----------|----------|
| Workflow Step | 审批流步骤 | 审批流程节点 |
| Workflow Error Code | 审批流异常类型 | 异常处理分类 |
| ApprovalMatrix_01 | 财务BP审核矩阵 | 审批人矩阵 |

## 5. 字段使用规范

### 5.1 自定义字段使用原则

1. **命名规范**: 自定义字段应在业务文档中明确定义用途
2. **数据一致性**: 同一字段在不同记录中应保持数据格式一致
3. **LOV关联**: 涉及分类的字段应通过LOV系统管理
4. **文档更新**: 新增字段用途应及时更新映射文档

### 5.2 LOV系统使用规范

1. **代码存储**: 业务表存储value_code，显示时关联获取value_meaning
2. **多语言支持**: 通过fnd_lov_value_tl表支持多语言显示
3. **权限控制**: 利用department_control和user_control实现数据权限
4. **数据维护**: LOV数据变更需要考虑历史数据兼容性

### 5.3 关联查询模式

#### 标准LOV查询模式
```sql
-- 获取单据的IIT分类中文名称
SELECT 
    h.document_num,
    vtl.value_meaning as iit_category_name
FROM exp_claim_header h
LEFT JOIN fnd_lov l ON l.lov_name = 'categories'
LEFT JOIN fnd_lov_value v ON v.lov_id = l.lov_id 
    AND v.value_code = h.column15
LEFT JOIN fnd_lov_value_tl vtl ON vtl.value_id = v.value_id 
    AND vtl.language = 'zh_CN'
```

#### 多字段LOV查询模式
```sql
-- 同时获取多个LOV字段的显示值
SELECT 
    h.document_num,
    iit.value_meaning as iit_category,
    prj.value_meaning as project_name,
    rt.value_meaning as receipt_type
FROM exp_claim_header h
-- IIT分类
LEFT JOIN fnd_lov l1 ON l1.lov_name = 'categories'
LEFT JOIN fnd_lov_value v1 ON v1.lov_id = l1.lov_id AND v1.value_code = h.column15
LEFT JOIN fnd_lov_value_tl iit ON iit.value_id = v1.value_id AND iit.language = 'zh_CN'
-- 项目号
LEFT JOIN fnd_lov l2 ON l2.lov_name = 'Project_number'
LEFT JOIN fnd_lov_value v2 ON v2.lov_id = l2.lov_id AND v2.value_code = h.column46
LEFT JOIN fnd_lov_value_tl prj ON prj.value_id = v2.value_id AND prj.language = 'zh_CN'
-- 单据类型
LEFT JOIN fnd_lov l3 ON l3.lov_name = 'receipts_type'
LEFT JOIN fnd_lov_value v3 ON v3.lov_id = l3.lov_id AND v3.value_code = h.internal_type
LEFT JOIN fnd_lov_value_tl rt ON rt.value_id = v3.value_id AND rt.language = 'zh_CN'
```

## 6. 常见问题与解决方案

### 6.1 字段值为空的处理

**问题**: 业务表中LOV相关字段值为空时，关联查询返回空结果
**解决方案**: 使用LEFT JOIN，并在SELECT中使用COALESCE处理空值

```sql
SELECT 
    h.document_num,
    COALESCE(vtl.value_meaning, h.column15, '未分类') as category_display
FROM exp_claim_header h
LEFT JOIN fnd_lov l ON l.lov_name = 'categories'
LEFT JOIN fnd_lov_value v ON v.lov_id = l.lov_id AND v.value_code = h.column15
LEFT JOIN fnd_lov_value_tl vtl ON vtl.value_id = v.value_id AND vtl.language = 'zh_CN'
```

### 6.2 LOV数据不存在的处理

**问题**: 业务表引用的value_code在LOV中不存在
**解决方案**: 
1. 数据清理：检查并修复不一致的数据
2. 查询兜底：在显示层提供默认值处理

### 6.3 部门层级查询性能优化

**问题**: 部门层级递归查询性能较差
**解决方案**: 
1. 使用WITH RECURSIVE优化递归查询
2. 考虑增加部门路径字段存储完整层级路径
3. 建立适当的索引

---

*本文档详细说明了系统中各表的字段映射关系，为开发人员提供准确的数据使用指导。*