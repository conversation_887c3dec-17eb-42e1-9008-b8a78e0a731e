# 开发指南文档

## 概述

本文档基于对康方报表管理系统成本控制模块的深入分析，为开发人员提供实用的开发指导和最佳实践，包括数据查询、报表开发、性能优化等方面的建议。

## 1. 核心开发原则

### 1.1 数据查询原则

1. **优先使用LEFT JOIN**: 保证主表数据完整性，避免因关联表缺失数据导致结果丢失
2. **理解"代码"与"名称"分离**: 业务表存储代码，显示时通过LOV系统获取中文名称
3. **善用自定义字段**: 重要业务信息往往存储在column1-column50等自定义字段中
4. **注意层级关系**: 部门等组织架构数据存在层级关系，需要递归或路径查询

### 1.2 性能优化原则

1. **合理使用索引**: 关联查询时确保连接字段有适当索引
2. **避免N+1查询**: 批量查询LOV数据，避免循环查询
3. **分页查询**: 大数据量查询必须使用分页
4. **缓存LOV数据**: LOV数据相对稳定，适合缓存处理

## 2. 常用查询模式

### 2.1 基础单据查询

```sql
-- 获取费用单据基本信息
SELECT 
    h.header_id,
    h.document_num as 单据编号,
    h.total_amount as 总金额,
    su.full_name as 提交人,
    cu.full_name as 承担人,
    sd.department_code as 提交部门,
    cd.department_code as 承担部门,
    h.submit_date as 提交日期,
    CASE h.status
        WHEN 'DRAFT' THEN '草稿'
        WHEN 'SUBMIT' THEN '已提交'
        WHEN 'APPROVE' THEN '已审批'
        WHEN 'REJECT' THEN '已驳回'
        ELSE h.status
    END as 单据状态
FROM exp_claim_header h
LEFT JOIN fnd_user su ON su.user_id = h.submit_user
LEFT JOIN fnd_user cu ON cu.user_id = h.charge_user  
LEFT JOIN fnd_department sd ON sd.department_id = h.submit_department
LEFT JOIN fnd_department cd ON cd.department_id = h.charge_department
WHERE h.company_id = ? 
    AND h.submit_date >= ?
    AND h.submit_date <= ?
ORDER BY h.submit_date DESC
```

### 2.2 LOV关联查询模式

```sql
-- 获取带LOV翻译的完整单据信息
SELECT 
    h.document_num as 单据编号,
    rt.value_meaning as 单据类型,
    iit.value_meaning as IIT分类,
    prj.value_meaning as 项目名称,
    h.total_amount as 总金额,
    su.full_name as 提交人
FROM exp_claim_header h
LEFT JOIN fnd_user su ON su.user_id = h.submit_user
-- 单据类型LOV
LEFT JOIN fnd_lov l1 ON l1.lov_name = 'receipts_type'
LEFT JOIN fnd_lov_value v1 ON v1.lov_id = l1.lov_id AND v1.value_code = h.internal_type
LEFT JOIN fnd_lov_value_tl rt ON rt.value_id = v1.value_id AND rt.language = 'zh_CN'
-- IIT分类LOV  
LEFT JOIN fnd_lov l2 ON l2.lov_name = 'categories'
LEFT JOIN fnd_lov_value v2 ON v2.lov_id = l2.lov_id AND v2.value_code = h.column15
LEFT JOIN fnd_lov_value_tl iit ON iit.value_id = v2.value_id AND iit.language = 'zh_CN'
-- 项目号LOV
LEFT JOIN fnd_lov l3 ON l3.lov_name = 'Project_number'
LEFT JOIN fnd_lov_value v3 ON v3.lov_id = l3.lov_id AND v3.value_code = h.column46
LEFT JOIN fnd_lov_value_tl prj ON prj.value_id = v3.value_id AND prj.language = 'zh_CN'
WHERE h.company_id = ?
```

### 2.3 部门层级查询

```sql
-- 查询部门及其上级部门信息（最多3级）
WITH RECURSIVE dept_tree AS (
    -- 起始部门
    SELECT 
        department_id,
        department_code,
        supervisor_id,
        column2 as region_code,
        1 as level,
        CAST(department_code AS varchar(1000)) as dept_path
    FROM fnd_department 
    WHERE department_id = ?
    
    UNION ALL
    
    -- 递归上级部门
    SELECT 
        d.department_id,
        d.department_code,
        d.supervisor_id,
        d.column2,
        dt.level + 1,
        CONCAT(d.department_code, ' -> ', dt.dept_path)
    FROM fnd_department d
    INNER JOIN dept_tree dt ON d.department_id = dt.supervisor_id
    WHERE dt.level < 3
),
-- 获取片区名称
dept_with_region AS (
    SELECT 
        dt.*,
        lov_region.value_meaning as region_name
    FROM dept_tree dt
    LEFT JOIN fnd_lov l ON l.lov_name = 'CBZXDX'
    LEFT JOIN fnd_lov_value v ON v.lov_id = l.lov_id AND v.value_code = dt.region_code
    LEFT JOIN fnd_lov_value_tl lov_region ON lov_region.value_id = v.value_id 
        AND lov_region.language = 'zh_CN'
)
SELECT * FROM dept_with_region ORDER BY level;
```

### 2.4 费用明细查询

```sql
-- 查询费用单据明细（包含终端信息）
SELECT 
    h.document_num as 单据编号,
    h.total_amount as 单据总金额,
    l.line_id as 行号,
    l.amount as 行金额,
    l.description as 费用描述,
    terminal.value_meaning as 终端名称,
    l.cost_center_id as 成本中心
FROM exp_claim_header h
INNER JOIN exp_claim_line l ON l.header_id = h.header_id
-- 终端LOV
LEFT JOIN fnd_lov lov ON lov.lov_name = 'ZD01'
LEFT JOIN fnd_lov_value lv ON lv.lov_id = lov.lov_id AND lv.value_code = l.column42
LEFT JOIN fnd_lov_value_tl terminal ON terminal.value_id = lv.value_id 
    AND terminal.language = 'zh_CN'
WHERE h.header_id = ?
ORDER BY l.line_id
```

## 3. 报表开发最佳实践

### 3.1 报表查询结构设计

```sql
-- 标准报表查询结构模板
WITH base_data AS (
    -- 基础数据查询，包含必要的筛选条件
    SELECT 
        h.header_id,
        h.document_num,
        h.internal_type,
        h.submit_date,
        h.total_amount,
        h.submit_user,
        h.charge_department,
        h.column15, -- IIT分类代码
        h.column46  -- 项目号代码
    FROM exp_claim_header h
    WHERE h.company_id = ?
        AND h.submit_date BETWEEN ? AND ?
        AND h.status IN ('SUBMIT', 'APPROVE')
),
enriched_data AS (
    -- 数据丰富化，添加LOV翻译和关联信息
    SELECT 
        bd.*,
        u.full_name as submit_user_name,
        d.department_code as charge_dept_code,
        rt.value_meaning as receipt_type_name,
        cat.value_meaning as category_name,
        prj.value_meaning as project_name
    FROM base_data bd
    LEFT JOIN fnd_user u ON u.user_id = bd.submit_user
    LEFT JOIN fnd_department d ON d.department_id = bd.charge_department
    -- LOV翻译
    LEFT JOIN fnd_lov l1 ON l1.lov_name = 'receipts_type'
    LEFT JOIN fnd_lov_value v1 ON v1.lov_id = l1.lov_id AND v1.value_code = bd.internal_type
    LEFT JOIN fnd_lov_value_tl rt ON rt.value_id = v1.value_id AND rt.language = 'zh_CN'
    
    LEFT JOIN fnd_lov l2 ON l2.lov_name = 'categories'  
    LEFT JOIN fnd_lov_value v2 ON v2.lov_id = l2.lov_id AND v2.value_code = bd.column15
    LEFT JOIN fnd_lov_value_tl cat ON cat.value_id = v2.value_id AND cat.language = 'zh_CN'
    
    LEFT JOIN fnd_lov l3 ON l3.lov_name = 'Project_number'
    LEFT JOIN fnd_lov_value v3 ON v3.lov_id = l3.lov_id AND v3.value_code = bd.column46  
    LEFT JOIN fnd_lov_value_tl prj ON prj.value_id = v3.value_id AND prj.language = 'zh_CN'
)
-- 最终结果输出
SELECT 
    document_num as "单据编号",
    receipt_type_name as "单据类型", 
    category_name as "IIT分类",
    project_name as "项目名称",
    submit_user_name as "提交人",
    charge_dept_code as "承担部门",
    total_amount as "金额",
    submit_date as "提交日期"
FROM enriched_data
ORDER BY submit_date DESC;
```

### 3.2 预算状态判断逻辑

```sql
-- 预算状态判断（基于单据类型）
SELECT 
    h.document_num,
    h.internal_type,
    rt.value_meaning as 单据类型,
    CASE 
        WHEN h.internal_type LIKE '%01' THEN '占用'  -- 申请类单据
        WHEN h.internal_type LIKE '%02' THEN '消耗'  -- 报销类单据
        WHEN h.internal_type LIKE '%03' THEN '消耗'  -- 其他消耗类
        ELSE '未知'
    END as 预算状态,
    h.total_amount
FROM exp_claim_header h
LEFT JOIN fnd_lov l ON l.lov_name = 'receipts_type'
LEFT JOIN fnd_lov_value v ON v.lov_id = l.lov_id AND v.value_code = h.internal_type
LEFT JOIN fnd_lov_value_tl rt ON rt.value_id = v.value_id AND rt.language = 'zh_CN'
WHERE h.company_id = ?
```

## 4. Java代码开发指导

### 4.1 Mapper层开发

```java
// 定义查询DTO
@Data
public class ExpenseReportDTO {
    private String documentNum;
    private String receiptTypeName;
    private String categoryName;
    private String projectName;
    private String submitUserName;
    private String chargeDeptCode;
    private BigDecimal totalAmount;
    private Date submitDate;
}

// Mapper接口定义
@Mapper
public interface ExpenseReportMapper {
    
    /**
     * 查询费用报表数据
     */
    List<ExpenseReportDTO> selectExpenseReport(ExpenseReportQuery query);
    
    /**
     * 查询LOV值列表
     */
    List<LovValueDTO> selectLovValues(@Param("lovName") String lovName, 
                                     @Param("companyId") Integer companyId);
    
    /**
     * 查询部门层级信息
     */
    List<DepartmentHierarchyDTO> selectDepartmentHierarchy(@Param("deptId") Integer deptId, 
                                                          @Param("maxLevel") Integer maxLevel);
}
```

### 4.2 Service层开发

```java
@Service
@Transactional(readOnly = true)
public class ExpenseReportServiceImpl implements ExpenseReportService {
    
    @Autowired
    private ExpenseReportMapper expenseReportMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 查询费用报表（带缓存优化）
     */
    public PageResult<ExpenseReportDTO> getExpenseReport(ExpenseReportQuery query) {
        // 参数校验
        validateQuery(query);
        
        // 设置分页
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        
        // 查询数据
        List<ExpenseReportDTO> list = expenseReportMapper.selectExpenseReport(query);
        
        // 返回分页结果
        return PageResult.of(list);
    }
    
    /**
     * 获取LOV数据（带缓存）
     */
    public List<LovValueDTO> getLovValues(String lovName, Integer companyId) {
        String cacheKey = String.format("lov:%s:%d", lovName, companyId);
        
        // 先从缓存获取
        List<LovValueDTO> cached = (List<LovValueDTO>) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 查询数据库
        List<LovValueDTO> result = expenseReportMapper.selectLovValues(lovName, companyId);
        
        // 存入缓存（1小时过期）
        redisTemplate.opsForValue().set(cacheKey, result, 1, TimeUnit.HOURS);
        
        return result;
    }
    
    private void validateQuery(ExpenseReportQuery query) {
        if (query.getCompanyId() == null) {
            throw new BusinessException("公司ID不能为空");
        }
        if (query.getStartDate() == null || query.getEndDate() == null) {
            throw new BusinessException("查询时间范围不能为空");  
        }
        // 限制查询时间范围不超过1年
        if (ChronoUnit.DAYS.between(query.getStartDate().toLocalDate(), 
                                   query.getEndDate().toLocalDate()) > 365) {
            throw new BusinessException("查询时间范围不能超过1年");
        }
    }
}
```

### 4.3 Controller层开发

```java
@RestController
@RequestMapping("/expense/report")
@Api(tags = "费用报表管理")
public class ExpenseReportController {
    
    @Autowired
    private ExpenseReportService expenseReportService;
    
    @PostMapping("/list")
    @ApiOperation("查询费用报表")
    public AjaxResult getExpenseReport(@RequestBody @Valid ExpenseReportQuery query) {
        PageResult<ExpenseReportDTO> result = expenseReportService.getExpenseReport(query);
        return AjaxResult.success(result);
    }
    
    @GetMapping("/lov/{lovName}")
    @ApiOperation("获取LOV数据")
    public AjaxResult getLovValues(@PathVariable String lovName, 
                                  @RequestParam Integer companyId) {
        List<LovValueDTO> result = expenseReportService.getLovValues(lovName, companyId);
        return AjaxResult.success(result);
    }
    
    @PostMapping("/export")
    @ApiOperation("导出费用报表")
    public void exportExpenseReport(@RequestBody @Valid ExpenseReportQuery query, 
                                   HttpServletResponse response) {
        // 查询数据（不分页）
        query.setPageNum(null);
        query.setPageSize(null);
        List<ExpenseReportDTO> data = expenseReportService.getExpenseReportForExport(query);
        
        // Excel导出
        ExcelUtils.export(response, "费用报表", ExpenseReportDTO.class, data);
    }
}
```

## 5. 性能优化建议

### 5.1 数据库层面优化

1. **索引优化**
```sql
-- 建议的关键索引
CREATE INDEX idx_exp_claim_header_query ON exp_claim_header(company_id, submit_date, status);
CREATE INDEX idx_exp_claim_header_user_dept ON exp_claim_header(submit_user, charge_department);
CREATE INDEX idx_exp_claim_line_header ON exp_claim_line(header_id, company_id);
CREATE INDEX idx_fnd_lov_value_code ON fnd_lov_value(lov_id, value_code, enabled_flag);
CREATE INDEX idx_fnd_department_supervisor ON fnd_department(supervisor_id, company_id);
```

2. **查询优化**
   - 避免在WHERE子句中使用函数
   - 合理使用EXISTS替代IN
   - 大表关联时优先过滤条件

### 5.2 应用层面优化

1. **缓存策略**
   - LOV数据使用Redis缓存
   - 部门层级数据缓存
   - 用户基本信息缓存

2. **分页查询**
   - 大数据量查询必须分页
   - 考虑使用游标分页优化深分页性能

3. **批量处理**
   - 批量查询LOV数据
   - 批量处理数据导出

## 6. 常见问题与解决方案

### 6.1 数据不一致问题

**问题**: LOV关联查询返回空值
**原因**: 
1. 业务表中的code值在LOV中不存在
2. LOV数据被禁用但业务表仍在引用

**解决方案**:
```sql
-- 数据一致性检查脚本
-- 检查不存在的LOV引用
SELECT DISTINCT h.column15
FROM exp_claim_header h
LEFT JOIN fnd_lov l ON l.lov_name = 'categories'
LEFT JOIN fnd_lov_value v ON v.lov_id = l.lov_id AND v.value_code = h.column15
WHERE h.column15 IS NOT NULL 
  AND v.value_id IS NULL;

-- 修复方案：添加缺失的LOV数据或清理无效引用
```

### 6.2 性能问题

**问题**: 复杂LOV关联查询性能差
**解决方案**:
1. 使用视图预计算常用LOV关联
2. 增加LOV数据缓存
3. 优化查询SQL，减少不必要的关联

### 6.3 部门层级查询问题

**问题**: 递归查询深度过大导致性能问题
**解决方案**:
1. 限制递归深度
2. 增加部门路径字段存储完整路径
3. 使用缓存存储部门层级关系

## 7. 代码生成工具使用

### 7.1 MyBatis Generator配置

```xml
<!-- mybatis-generator配置，针对成本控制模块 -->
<generatorConfiguration>
    <context id="costControl" targetRuntime="MyBatis3">
        <!-- 数据库连接 -->
        <jdbcConnection driverClass="com.microsoft.sqlserver.jdbc.SQLServerDriver"
                       connectionURL="*******************************************************"
                       userId="username" password="password"/>
        
        <!-- 生成实体类 -->
        <javaModelGenerator targetPackage="com.akesobio.report.costControl.domain" 
                           targetProject="src/main/java"/>
        
        <!-- 生成Mapper XML -->
        <sqlMapGenerator targetPackage="mapper.costControl" 
                        targetProject="src/main/resources"/>
        
        <!-- 生成Mapper接口 -->
        <javaClientGenerator type="XMLMAPPER" 
                           targetPackage="com.akesobio.report.costControl.mapper"
                           targetProject="src/main/java"/>
        
        <!-- 表配置 -->
        <table tableName="exp_claim_header" domainObjectName="ExpClaimHeader"/>
        <table tableName="exp_claim_line" domainObjectName="ExpClaimLine"/>
        <table tableName="fnd_lov" domainObjectName="FndLov"/>
        <table tableName="fnd_lov_value" domainObjectName="FndLovValue"/>
        <table tableName="fnd_lov_value_tl" domainObjectName="FndLovValueTl"/>
    </context>
</generatorConfiguration>
```

## 8. 测试指导

### 8.1 单元测试示例

```java
@SpringBootTest
@Transactional
class ExpenseReportServiceTest {
    
    @Autowired
    private ExpenseReportService expenseReportService;
    
    @Test
    void testGetExpenseReport() {
        // 准备测试数据
        ExpenseReportQuery query = new ExpenseReportQuery();
        query.setCompanyId(1);
        query.setStartDate(Date.valueOf("2024-01-01"));
        query.setEndDate(Date.valueOf("2024-12-31"));
        query.setPageNum(1);
        query.setPageSize(10);
        
        // 执行查询
        PageResult<ExpenseReportDTO> result = expenseReportService.getExpenseReport(query);
        
        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getList()).isNotNull();
        assertThat(result.getTotal()).isGreaterThanOrEqualTo(0);
    }
    
    @Test
    void testLovCache() {
        // 第一次查询
        List<LovValueDTO> result1 = expenseReportService.getLovValues("categories", 1);
        
        // 第二次查询（应该从缓存获取）
        List<LovValueDTO> result2 = expenseReportService.getLovValues("categories", 1);
        
        // 验证结果一致
        assertThat(result1).isEqualTo(result2);
    }
}
```

---

*本文档提供了基于表关系分析的完整开发指导，涵盖了从数据库查询到Java代码实现的各个方面，为开发人员提供实用的技术指南。*