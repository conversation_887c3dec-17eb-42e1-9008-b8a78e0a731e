我将帮您分析 `akesobio-report/src/main/java/com/akesobio/report/costControl/doc/cpkfswsql` 目录中的 SQL 文件和相关文档。让我先扫描该目录的内容。

现在让我逐一分析这些文件，首先查看主要的 SQL 文件：

文件很长，让我继续查看其他相关文档来了解表结构和关系：

现在让我查看其他相关文档：

现在让我查看单据类型与代码映射关系：

现在让我查看注意事项文档：

现在让我使用 codebase-retrieval 工具来获取更多关于数据库表结构的信息：

现在让我分析 SQL 文件的更多内容来获取完整的表结构信息：

现在让我查看更多核心业务表的结构：

现在让我查看 fnd_lov 相关表的结构：

现在让我查看部门表和用户表的结构：

现在让我查看用户表的结构：

基于我对 SQL 文件和相关文档的详细分析，现在我将生成完整的数据库表关系图和开发指导文档。

## 康方报表管理系统 - 成本控制模块数据库表关系分析报告

基于对 `cpkfswsql` 目录中 SQL 文件和相关文档的深入分析，我为您提供完整的数据库表关系图和开发指导文档。

### 1. 数据库概览

该数据库包含约 **600+** 张表，主要涵盖以下业务模块：
- **费用管理系统** (exp_claim_*)
- **值列表系统** (fnd_lov_*)
- **组织架构管理** (fnd_department_*, fnd_user_*)
- **预算管理** (exp_budget_*)
- **银企直连** (bank_*, ap_payment_*)
- **工作流引擎** (wf_*)
- **附件管理** (attachment_*)

### 2. 核心业务表关系图

### 3. LOV值列表系统详细关系图

### 4. 银企直连与付款系统关系图

### 5. 关键表字段说明

#### 5.1 exp_claim_header (费用单据头表)

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| header_id | int | 主键 | 单据唯一标识 |
| document_num | varchar(64) | 单据编号 | 业务单据编号，唯一 |
| internal_type | varchar(64) | 内部类型 | 对应 receipts_type LOV |
| status | varchar(16) | 单据状态 | DRAFT/SUBMIT/APPROVE等 |
| column46 | varchar(255) | 项目号 | 对应 Project_number LOV |
| column15 | varchar(255) | IIT分类 | 对应 categories LOV |
| submit_user | int | 提交人 | 关联 fnd_user.user_id |
| charge_user | int | 承担人 | 关联 fnd_user.user_id |
| submit_department | int | 提交部门 | 关联 fnd_department.department_id |
| charge_department | int | 承担部门 | 关联 fnd_department.department_id |

#### 5.2 fnd_department (部门表)

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| department_id | int | 主键 | 部门唯一标识 |
| supervisor_id | int | 外键 | 上级部门ID，形成层级 |
| column2 | varchar(255) | 所属片区 | 对应 CBZXDX LOV |
| natural_level | int | 自然层级 | 部门在组织架构中的层级 |
| department_code | varchar(64) | 部门代码 | 部门业务编码 |

#### 5.3 LOV系统核心逻辑

1. **fnd_lov**: 定义值列表类型（如 categories、Project_number 等）
2. **fnd_lov_value**: 存储每个类型下的具体值代码
3. **fnd_lov_value_tl**: 存储值的多语言显示名称
4. **业务表关联**: 通过 value_code 与业务表的自定义字段关联

### 6. 重要的LOV类型代码映射

根据文档分析，以下是关键的LOV类型：

| LOV类型代码 | 中文名称 | 关联字段示例 | 说明 |
|-------------|----------|--------------|------|
| categories | IIT分类 | exp_claim_header.column15 | 研究者发起IIT等分类 |
| Project_number | 项目号 | exp_claim_header.column46 | 项目编号主数据 |
| receipts_type | 单据类型 | exp_claim_header.internal_type | YXFY01、YXFY02等 |
| ZD01 | 终端 | exp_claim_line.column42 | 终端代码 |
| CBZXDX | 部门所属片区 | fnd_department.column2 | 片区划分 |
| YW01 | 费用类型 | exp_claim_line.line_type | 费用分类 |

### 7. 单据类型与代码映射

| 类型名称 | 类型代码 | 优先级 | 业务说明 |
|----------|----------|--------|----------|
| FSAA-差旅申请 | YXCL01 | 10 | 差旅申请单据 |
| FSAB-差旅报销 | YXCL02 | 20 | 差旅报销单据 |
| FSBA-费用申请 | YXFY01 | 30 | 费用申请单据 |
| FSBB-费用报销 | YXFY02 | 40 | 费用报销单据 |
| FSEA-IIT立项申请 | YXIIT01 | 41 | IIT项目立项 |
| FSHB-对公预付 | YXDG01 | 60 | 对公预付款 |
| FSHC-对公应付 | YXDG02 | 70 | 对公应付款 |

### 8. 开发使用指南

#### 8.1 查询LOV显示值的标准模式

```sql
-- 获取项目号的中文名称
SELECT h.document_num, vtl.value_meaning as project_name
FROM exp_claim_header h
LEFT JOIN fnd_lov l ON l.lov_name = 'Project_number' AND l.company_id = h.company_id
LEFT JOIN fnd_lov_value v ON v.lov_id = l.lov_id AND v.value_code = h.column46
LEFT JOIN fnd_lov_value_tl vtl ON vtl.value_id = v.value_id AND vtl.language = 'zh_CN'
WHERE h.company_id = ?
```

#### 8.2 部门层级查询模式

```sql
-- 获取部门的上级大区信息
WITH RECURSIVE dept_hierarchy AS (
    SELECT department_id, supervisor_id, column2, 1 as level
    FROM fnd_department 
    WHERE department_id = ?
    UNION ALL
    SELECT d.department_id, d.supervisor_id, d.column2, dh.level + 1
    FROM fnd_department d
    INNER JOIN dept_hierarchy dh ON d.department_id = dh.supervisor_id
)
SELECT * FROM dept_hierarchy WHERE level <= 3;
```

#### 8.3 单据状态判断逻辑

根据文档分析，单据状态判断建议：
- 直接使用 `exp_claim_header.status` 字段
- 状态映射：DRAFT(草稿)、SUBMIT(已提交)、APPROVE(已审批)、REJECT(已驳回)

#### 8.4 预算状态判断逻辑

根据业务逻辑：
- 申请类单据（如YXFY01）：预算状态为"占用"
- 报销类单据（如YXFY02）：预算状态为"消耗"

### 9. 性能优化建议

#### 9.1 索引优化
- **exp_claim_header**: 在 `(company_id, status, creation_date)` 上建立复合索引
- **fnd_lov_value**: 在 `(lov_id, value_code, enabled_flag)` 上建立复合索引
- **bank_statement_log**: 在 `(company_id, receipt_date, status)` 上建立复合索引

#### 9.2 查询优化
- 使用 `LEFT JOIN` 而非 `INNER JOIN` 来保证主表数据完整性
- LOV查询时优先过滤 `enabled_flag = 'Y'`
- 大数据量查询时使用分页和时间范围限制

#### 9.3 常用查询模式
- 单据列表查询：按公司ID、状态、时间范围过滤
- LOV转换：批量转换时使用 `IN` 条件而非多次单独查询
- 部门层级：使用递归CTE或预计算层级路径

### 10. 注意事项

1. **数据关联准确性**：业务表中的代码字段需要通过LOV系统转换为显示名称
2. **自定义字段使用**：关键业务信息存储在 `columnXX` 字段中，需要准确映射
3. **多语言支持**：显示名称需要根据用户语言偏好从 `*_tl` 表获取
4. **权限控制**：LOV值可能有部门或用户级别的权限控制
5. **数据一致性**：跨表关联时注意 `company_id` 的一致性检查

---

*本文档基于 cpkfswsql 目录中的数据库结构分析生成，为开发人员提供数据库使用指导。建议在实际开发中结合具体业务需求进行调整。*
