# 康方报表管理系统 - 成本控制模块表关系图

## 概述

本文档基于 `cpkfswsql` 目录中的 SQL 文件和相关文档，为开发人员提供详细的表关系图，用于指导开发工作。

## 1. 核心业务表关系图

### 1.1 费用单据核心表关系

```mermaid
erDiagram
    exp_claim_header {
        int header_id PK "单据头ID"
        int company_id "公司ID"
        varchar document_num "单据编号"
        varchar internal_type "内部类型"
        varchar status "单据状态"
        int submit_user "提交人"
        int charge_user "承担人"
        int submit_department "提交部门"
        int charge_department "承担部门"
        decimal total_amount "总金额"
        varchar column1 "自定义字段1"
        varchar column2 "自定义字段2"
        varchar column46 "项目号(自定义)"
        datetime submit_date "提交日期"
        datetime creation_date "创建日期"
    }
    
    exp_claim_line {
        int line_id PK "行ID"
        int header_id FK "单据头ID"
        int company_id "公司ID"
        varchar line_type "行类型"
        decimal amount "金额"
        varchar description "描述"
        varchar column1 "自定义字段1"
        varchar column42 "终端代码(自定义)"
        int cost_center_id "成本中心"
        datetime creation_date "创建日期"
    }
    
    fnd_department {
        int department_id PK "部门ID"
        int company_id "公司ID"
        int supervisor_id "上级部门ID"
        varchar department_code "部门代码"
        varchar column1 "自定义字段1"
        varchar column2 "所属片区代码"
        int natural_level "自然层级"
        varchar enabled_flag "启用标志"
    }
    
    fnd_user {
        int user_id PK "用户ID"
        int company_id "公司ID" 
        varchar user_name "用户名"
        varchar full_name "全名"
        int department_id FK "部门ID"
        varchar employee_number "员工号"
        varchar enabled_flag "启用标志"
    }
    
    exp_claim_header ||--o{ exp_claim_line : "一对多"
    exp_claim_header }o--|| fnd_user : "提交人"
    exp_claim_header }o--|| fnd_user : "承担人"
    exp_claim_header }o--|| fnd_department : "提交部门"
    exp_claim_header }o--|| fnd_department : "承担部门"
    fnd_user }o--|| fnd_department : "所属部门"
    fnd_department }o--o| fnd_department : "上级部门"
```

## 2. 值列表(LOV)系统关系图

### 2.1 LOV核心表关系

```mermaid
erDiagram
    fnd_lov {
        int lov_id PK "LOV类型ID"
        int company_id "公司ID"
        varchar lov_name UK "LOV类型名称"
        varchar enabled_flag "启用标志"
        varchar department_control "部门管控"
        varchar user_control "人员管控"
    }
    
    fnd_lov_value {
        int value_id PK "值ID"
        int lov_id FK "LOV类型ID"
        int company_id "公司ID"
        varchar value_code "值代码"
        varchar enabled_flag "启用标志"
        varchar column1 "扩展字段1"
        varchar column2 "扩展字段2"
    }
    
    fnd_lov_value_tl {
        int value_id PK,FK "值ID"
        varchar language PK "语言"
        varchar value_meaning "显示名称"
        text description "描述"
    }
    
    fnd_lov_value_user {
        int company_id PK "公司ID"
        int value_id PK,FK "值ID"
        varchar staff_type PK "人员类型"
        int staff_id PK "人员ID"
        varchar child_include_flag "包含下级"
    }
    
    fnd_lov_value_department {
        int value_id PK,FK "值ID"
        int department_id PK "部门ID"
        varchar child_include_flag "包含下级"
    }
    
    fnd_lov ||--o{ fnd_lov_value : "一对多"
    fnd_lov_value ||--o{ fnd_lov_value_tl : "一对多"
    fnd_lov_value ||--o{ fnd_lov_value_user : "权限控制"
    fnd_lov_value ||--o{ fnd_lov_value_department : "部门控制"
```

### 2.2 业务表与LOV系统关联

```mermaid
erDiagram
    exp_claim_header {
        varchar column15 "IIT分类代码"
        varchar column46 "项目号代码"
        varchar internal_type "单据类型"
    }
    
    exp_claim_line {
        varchar column42 "终端代码"
        varchar line_type "费用类型"
    }
    
    fnd_department {
        varchar column2 "所属片区代码"
    }
    
    fnd_lov_value {
        varchar value_code "值代码"
    }
    
    exp_claim_header ||--o{ fnd_lov_value : "column15->categories"
    exp_claim_header ||--o{ fnd_lov_value : "column46->Project_number"
    exp_claim_header ||--o{ fnd_lov_value : "internal_type->receipts_type"
    exp_claim_line ||--o{ fnd_lov_value : "column42->ZD01"
    fnd_department ||--o{ fnd_lov_value : "column2->CBZXDX"
```

## 3. 预算管理表关系图

### 3.1 预算核心表关系

```mermaid
erDiagram
    exp_budget_plan {
        int budget_plan_id PK "预算计划ID"
        int company_id "公司ID"
        varchar budget_code "预算代码"
        int budget_type_id "预算类型ID"
        varchar status "状态"
        date start_date_active "生效开始日期"
        date end_date_active "生效结束日期"
    }
    
    exp_budget_plan_amount {
        int amount_id PK "预算金额ID"
        int budget_plan_id FK "预算计划ID" 
        int company_id "公司ID"
        decimal budget_amount "预算金额"
        decimal used_amount "已用金额"
        decimal available_amount "可用金额"
        int dimension1 "维度1"
        int dimension2 "维度2"
    }
    
    exp_claim_line_budget {
        int line_budget_id PK "行预算ID"
        int line_id FK "单据行ID"
        int budget_plan_id FK "预算计划ID"
        decimal budget_amount "预算金额"
        varchar budget_status "预算状态"
        varchar operation_type "操作类型"
    }
    
    exp_budget_plan ||--o{ exp_budget_plan_amount : "一对多"
    exp_claim_line }o--|| exp_claim_line_budget : "预算关联"
    exp_budget_plan_amount }o--|| exp_claim_line_budget : "预算使用"
```

## 4. 完整系统表关系图

### 4.1 系统整体架构

```mermaid
erDiagram
    %% 核心业务表
    exp_claim_header {
        int header_id PK
        varchar document_num UK
        varchar internal_type
        varchar status
        int submit_user FK
        int charge_user FK
        int submit_department FK
        int charge_department FK
        varchar column46 "项目号"
    }
    
    exp_claim_line {
        int line_id PK
        int header_id FK
        varchar column42 "终端代码"
        decimal amount
    }
    
    %% 组织架构表
    fnd_user {
        int user_id PK
        varchar user_name UK
        varchar full_name
        int department_id FK
    }
    
    fnd_department {
        int department_id PK
        varchar department_code UK
        int supervisor_id FK
        varchar column2 "片区代码"
    }
    
    %% LOV系统表
    fnd_lov {
        int lov_id PK
        varchar lov_name UK
    }
    
    fnd_lov_value {
        int value_id PK
        int lov_id FK
        varchar value_code UK
    }
    
    fnd_lov_value_tl {
        int value_id FK
        varchar language
        varchar value_meaning
    }
    
    %% 预算表
    exp_budget_plan {
        int budget_plan_id PK
        varchar budget_code UK
    }
    
    exp_claim_line_budget {
        int line_budget_id PK
        int line_id FK
        int budget_plan_id FK
    }
    
    %% 关系定义
    exp_claim_header ||--o{ exp_claim_line : "header_id"
    exp_claim_header }o--|| fnd_user : "submit_user"
    exp_claim_header }o--|| fnd_user : "charge_user"
    exp_claim_header }o--|| fnd_department : "submit_department"
    exp_claim_header }o--|| fnd_department : "charge_department"
    fnd_user }o--|| fnd_department : "department_id"
    fnd_department }o--o| fnd_department : "supervisor_id"
    
    fnd_lov ||--o{ fnd_lov_value : "lov_id"
    fnd_lov_value ||--o{ fnd_lov_value_tl : "value_id"
    
    exp_claim_line ||--o| exp_claim_line_budget : "line_id"
    exp_budget_plan ||--o{ exp_claim_line_budget : "budget_plan_id"
```

## 5. 关键字段说明

### 5.1 exp_claim_header 关键字段

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| header_id | int | 主键 | 单据唯一标识 |
| document_num | varchar(64) | 单据编号 | 业务单据编号，唯一 |
| internal_type | varchar(64) | 内部类型 | 对应 receipts_type LOV |
| status | varchar(16) | 单据状态 | DRAFT/SUBMIT/APPROVE等 |
| column46 | varchar(255) | 项目号 | 对应 Project_number LOV |
| column15 | varchar(255) | IIT分类 | 对应 categories LOV |

### 5.2 fnd_department 关键字段

| 字段名 | 类型 | 说明 | 业务含义 |
|--------|------|------|----------|
| department_id | int | 主键 | 部门唯一标识 |
| supervisor_id | int | 外键 | 上级部门ID，形成层级 |
| column2 | varchar(255) | 所属片区 | 对应 CBZXDX LOV |
| natural_level | int | 自然层级 | 部门在组织架构中的层级 |

### 5.3 LOV系统核心逻辑

1. **fnd_lov**: 定义值列表类型（如 categories、Project_number 等）
2. **fnd_lov_value**: 存储每个类型下的具体值代码
3. **fnd_lov_value_tl**: 存储值的多语言显示名称
4. **业务表关联**: 通过 value_code 与业务表的自定义字段关联

## 6. 开发使用指南

### 6.1 查询LOV显示值的标准模式

```sql
-- 获取项目号的中文名称
SELECT h.document_num, vtl.value_meaning as project_name
FROM exp_claim_header h
LEFT JOIN fnd_lov l ON l.lov_name = 'Project_number'
LEFT JOIN fnd_lov_value v ON v.lov_id = l.lov_id AND v.value_code = h.column46
LEFT JOIN fnd_lov_value_tl vtl ON vtl.value_id = v.value_id AND vtl.language = 'zh_CN'
```

### 6.2 部门层级查询模式

```sql
-- 获取部门的上级大区信息
WITH RECURSIVE dept_hierarchy AS (
    SELECT department_id, supervisor_id, column2, 1 as level
    FROM fnd_department 
    WHERE department_id = ?
    UNION ALL
    SELECT d.department_id, d.supervisor_id, d.column2, dh.level + 1
    FROM fnd_department d
    INNER JOIN dept_hierarchy dh ON d.department_id = dh.supervisor_id
)
SELECT * FROM dept_hierarchy WHERE level <= 3;
```

### 6.3 单据状态判断逻辑

根据文档分析，单据状态判断建议：
- 直接使用 `exp_claim_header.status` 字段
- 状态映射：DRAFT(草稿)、SUBMIT(已提交)、APPROVE(已审批)、REJECT(已驳回)

### 6.4 预算状态判断逻辑

根据业务逻辑：
- 申请类单据（如YXFY01）：预算状态为"占用"
- 报销类单据（如YXFY02）：预算状态为"消耗"

---

*本文档基于 cpkfswsql 目录中的数据库结构分析生成，为开发人员提供数据库使用指导。*