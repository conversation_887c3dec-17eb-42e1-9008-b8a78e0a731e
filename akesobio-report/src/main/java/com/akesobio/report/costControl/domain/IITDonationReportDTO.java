package com.akesobio.report.costControl.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 29、IIT赠药报表
 *
 * <AUTHOR>
 * @since 2025/6/25  16:13
 */
@Data
public class IITDonationReportDTO {
    @ExcelProperty("立项部门完整路径")
    private String setupDepartmentFullPath;
    @ExcelProperty("IIT分类")//column15
    private String iitClass;
    @ExcelProperty("项目号")//column46
    private String projectNumber;
    @ExcelProperty("领药系统单号")//h_drug_req.document_num
    private String receiveSysNumber;
    @ExcelProperty("提交日期")
    private String submitDate;
    @ExcelProperty("领药申请人")
    private String submitter;
    @ExcelProperty("单据状态")
    private String statusName;
    @ExcelProperty("费用承担部门完整路径")
    private String chargeDepartmentFullPath;
    @ExcelProperty("注射剂名称")
    private String injectionName;
    @ExcelProperty("总申请数量")
    private Integer totalNum;
    @ExcelProperty("赠药数量")
    private Integer injectionNum;
    @ExcelProperty("IIT中心")
    private String iitCenter;
    @ExcelProperty("科研科室")//column16
    private String researchDepartment;
    @ExcelProperty("收货人")
    private String receiver;
    @ExcelProperty("收货人联系电话")
    private String receiverPhone;
    @ExcelProperty("收货地址")
    private String receiveAddress;
    @ExcelProperty("签收部门")
    private String signDepartment;

    private Integer submitDepartmentId;
    // 费用承担部门ID（用于内部处理）
    private Integer chargeDepartmentId;
    // 原始状态代码（用于内部处理）
    private String status;
}
