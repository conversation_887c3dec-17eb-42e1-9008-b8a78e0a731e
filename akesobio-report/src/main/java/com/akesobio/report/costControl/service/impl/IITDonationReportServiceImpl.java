package com.akesobio.report.costControl.service.impl;

import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.costControl.domain.FndDepartmentDTO;
import com.akesobio.report.costControl.domain.IITDonationReportDTO;
import com.akesobio.report.costControl.mapper.FndDepartmentMapper;
import com.akesobio.report.costControl.mapper.IITDonationReportMapper;
import com.akesobio.report.costControl.mapper.query.IITDonationReportQuery;
import com.akesobio.report.costControl.service.IITDonationReportService;
import com.akesobio.report.costControl.utils.DepartmentPathUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 29、IIT赠药报表
 *
 * <AUTHOR>
 * @since 2025/6/25  16:13
 */
@Slf4j
@Service
public class IITDonationReportServiceImpl implements IITDonationReportService {
    @Resource
    IITDonationReportMapper iitDonationReportMapper;
    @Resource
    FndDepartmentMapper fndDepartmentMapper;

    @Override
    public List<IITDonationReportDTO> list(IITDonationReportQuery query) {
        List<IITDonationReportDTO> list = iitDonationReportMapper.list(query);
        this.buildDepartmentPaths(list);
        return list;
    }

    @Override
    public Page<IITDonationReportDTO> page(IITDonationReportQuery query) {
        Page<IITDonationReportDTO> page = iitDonationReportMapper.list(new Page<>(query.getPageNum(), query.getPageSize()), query);
        List<IITDonationReportDTO> list = page.getRecords();
        this.buildDepartmentPaths(list);
        page.setRecords(list);
        return page;
    }

    /**
     * 构建部门完整路径
     *
     * @param list 报表数据列表
     */
    public void buildDepartmentPaths(List<IITDonationReportDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取所有部门数据
            List<FndDepartmentDTO> departmentList = fndDepartmentMapper.list();
            if (departmentList == null || departmentList.isEmpty()) {
                log.warn("未获取到部门数据，无法构建部门路径");
                return;
            }

            log.info("开始构建部门路径，数据条数: {}, 部门总数: {}", list.size(), departmentList.size());

            // 为每条记录构建部门路径
            list.forEach(item -> {
                try {
                    // 构建立项部门完整路径
                    if (item.getSubmitDepartmentId() != null) {
                        String setupPath = DepartmentPathUtils.buildFullDepartmentPath(
                                item.getSubmitDepartmentId(), departmentList);
                        item.setSetupDepartmentFullPath(setupPath);

                        if (StringUtils.isEmpty(setupPath)) {
                            log.debug("立项部门路径为空，部门ID: {}", item.getSubmitDepartmentId());
                        }
                    }

                    // 构建费用承担部门完整路径
                    if (item.getChargeDepartmentId() != null) {
                        String chargePath = DepartmentPathUtils.buildFullDepartmentPath(
                                item.getChargeDepartmentId(), departmentList);
                        item.setChargeDepartmentFullPath(chargePath);

                        if (StringUtils.isEmpty(chargePath)) {
                            log.debug("费用承担部门路径为空，部门ID: {}", item.getChargeDepartmentId());
                        }
                    }

                } catch (Exception e) {
                    log.error("构建单条记录部门路径失败，领药系统单号: {}", item.getReceiveSysNumber(), e);
                }
            });

            log.info("部门路径构建完成");

        } catch (Exception e) {
            log.error("构建部门路径失败", e);
        }
    }

    @Override
    public void setUpDepartment(List<IITDonationReportDTO> list) {
        // 保留原方法以保持向后兼容性，但实际调用新的方法
        buildDepartmentPaths(list);
    }
}