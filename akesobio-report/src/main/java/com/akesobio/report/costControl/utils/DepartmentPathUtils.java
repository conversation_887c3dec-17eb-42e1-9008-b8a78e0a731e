package com.akesobio.report.costControl.utils;

import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.costControl.domain.FndDepartmentDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门路径构建工具类
 * 
 * <AUTHOR>
 * @since 2025/8/1
 */
@Slf4j
public class DepartmentPathUtils {
    
    /**
     * 路径分隔符
     */
    private static final String PATH_SEPARATOR = "_";
    
    /**
     * 最大递归层级（防止无限递归）
     */
    private static final int MAX_LEVEL = 10;
    
    /**
     * 需要过滤的部门名称关键词
     */
    private static final Set<String> FILTER_KEYWORDS = new HashSet<>(Arrays.asList("本部"));

    /**
     * 构建完整的部门路径
     * 
     * @param departmentId 部门ID
     * @param departmentList 所有部门列表
     * @return 完整的部门路径，格式：商业运营部_营销中心_北中国区_北中国区本部_京蒙大区_京蒙大区本部_北京5区
     */
    public static String buildFullDepartmentPath(Integer departmentId, List<FndDepartmentDTO> departmentList) {
        if (departmentId == null || departmentList == null || departmentList.isEmpty()) {
            return "";
        }
        
        try {
            // 构建部门ID到部门对象的映射
            Map<Integer, FndDepartmentDTO> departmentMap = departmentList.stream()
                    .filter(dept -> dept.getDepartmentId() != null)
                    .collect(Collectors.toMap(
                            FndDepartmentDTO::getDepartmentId, 
                            dept -> dept, 
                            (existing, replacement) -> existing // 如果有重复ID，保留第一个
                    ));
            
            // 递归构建路径
            List<String> pathList = new ArrayList<>();
            buildPathRecursive(departmentId, departmentMap, pathList, new HashSet<>(), 0);
            
            // 反转路径（从根部门到当前部门）
            Collections.reverse(pathList);
            
            // 连接路径
            return String.join(PATH_SEPARATOR, pathList);
            
        } catch (Exception e) {
            log.error("构建部门路径失败，部门ID: {}", departmentId, e);
            return "";
        }
    }
    
    /**
     * 递归构建部门路径
     * 
     * @param departmentId 当前部门ID
     * @param departmentMap 部门映射
     * @param pathList 路径列表
     * @param visitedIds 已访问的部门ID集合（防止循环引用）
     * @param level 当前递归层级
     */
    private static void buildPathRecursive(Integer departmentId, 
                                         Map<Integer, FndDepartmentDTO> departmentMap,
                                         List<String> pathList, 
                                         Set<Integer> visitedIds, 
                                         int level) {
        
        // 防止无限递归
        if (level > MAX_LEVEL) {
            log.warn("部门路径构建达到最大递归层级，停止递归。部门ID: {}", departmentId);
            return;
        }
        
        // 防止循环引用
        if (visitedIds.contains(departmentId)) {
            log.warn("检测到部门循环引用，停止递归。部门ID: {}", departmentId);
            return;
        }
        
        FndDepartmentDTO department = departmentMap.get(departmentId);
        if (department == null) {
            return;
        }
        
        visitedIds.add(departmentId);
        
        // 处理部门名称
        String departmentName = cleanDepartmentName(department.getDepartmentName());
        if (StringUtils.isNotEmpty(departmentName)) {
            pathList.add(departmentName);
        }
        
        // 递归处理上级部门
        if (department.getSupervisorId() != null && !department.getSupervisorId().equals(departmentId)) {
            buildPathRecursive(department.getSupervisorId(), departmentMap, pathList, visitedIds, level + 1);
        }
        
        visitedIds.remove(departmentId);
    }
    
    /**
     * 清理部门名称
     * 
     * @param departmentName 原始部门名称
     * @return 清理后的部门名称
     */
    private static String cleanDepartmentName(String departmentName) {
        if (StringUtils.isEmpty(departmentName)) {
            return "";
        }
        
        // 去除非中文字符（保留中文、数字、字母）
        String cleaned = departmentName.replaceAll("[^\\u4e00-\\u9fa5\\w]", "");
        
        // 检查是否需要过滤（如果包含"本部"等关键词，可以选择保留或过滤）
        // 根据业务需求，这里保留包含"本部"的部门名称
        return cleaned;
    }
    
    /**
     * 构建简化的部门路径（过滤掉"本部"等关键词）
     * 
     * @param departmentId 部门ID
     * @param departmentList 所有部门列表
     * @return 简化的部门路径
     */
    public static String buildSimplifiedDepartmentPath(Integer departmentId, List<FndDepartmentDTO> departmentList) {
        String fullPath = buildFullDepartmentPath(departmentId, departmentList);
        if (StringUtils.isEmpty(fullPath)) {
            return "";
        }
        
        // 分割路径并过滤
        String[] pathParts = fullPath.split(PATH_SEPARATOR);
        List<String> filteredParts = Arrays.stream(pathParts)
                .filter(part -> !shouldFilterDepartment(part))
                .collect(Collectors.toList());
        
        return String.join(PATH_SEPARATOR, filteredParts);
    }
    
    /**
     * 判断是否应该过滤该部门名称
     * 
     * @param departmentName 部门名称
     * @return 是否应该过滤
     */
    private static boolean shouldFilterDepartment(String departmentName) {
        if (StringUtils.isEmpty(departmentName)) {
            return true;
        }
        
        return FILTER_KEYWORDS.stream().anyMatch(departmentName::contains);
    }
    
    /**
     * 获取部门路径的层级数
     * 
     * @param departmentPath 部门路径
     * @return 层级数
     */
    public static int getDepartmentPathLevel(String departmentPath) {
        if (StringUtils.isEmpty(departmentPath)) {
            return 0;
        }
        
        return departmentPath.split(PATH_SEPARATOR).length;
    }
    
    /**
     * 获取部门路径的指定层级部门名称
     * 
     * @param departmentPath 部门路径
     * @param level 层级（从1开始，1表示最高级）
     * @return 指定层级的部门名称
     */
    public static String getDepartmentNameByLevel(String departmentPath, int level) {
        if (StringUtils.isEmpty(departmentPath) || level < 1) {
            return "";
        }
        
        String[] pathParts = departmentPath.split(PATH_SEPARATOR);
        if (level > pathParts.length) {
            return "";
        }
        
        return pathParts[level - 1];
    }
    
    /**
     * 验证部门路径格式是否正确
     * 
     * @param departmentPath 部门路径
     * @return 是否格式正确
     */
    public static boolean isValidDepartmentPath(String departmentPath) {
        if (StringUtils.isEmpty(departmentPath)) {
            return false;
        }
        
        // 检查是否包含分隔符
        if (!departmentPath.contains(PATH_SEPARATOR)) {
            // 单个部门名称也是有效的
            return StringUtils.isNotEmpty(departmentPath.trim());
        }
        
        // 检查是否有空的部分
        String[] pathParts = departmentPath.split(PATH_SEPARATOR);
        return Arrays.stream(pathParts).allMatch(part -> StringUtils.isNotEmpty(part.trim()));
    }
}
