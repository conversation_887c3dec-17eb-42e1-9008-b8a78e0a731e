<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.costControl.mapper.IITDonationReportMapper">

    <select id="list" parameterType="com.akesobio.report.costControl.mapper.query.IITDonationReportQuery"
            resultType="com.akesobio.report.costControl.domain.IITDonationReportDTO">
        -- 29、IIT赠药报表
        -- 该查询为最终修正版本，用于查找所有“领药申请单”（类型YXIIT02），
        -- 并通过单据关联，追溯到源头的“IIT立项申请单”（类型YXIIT01），
        -- 最终根据详细的字段映射提取所需信息，并包含完整的部门层级结构。
        -- 本版本已修复费用承担部门层级为空，以及部分字段显示ID的问题。
        -- 1. 修正了“提交日期”字段，确保正确取数。
        -- 2. 增加了“总申请数量”字段，用于统计每个领药申请单的总数量。
        -- 3. 【本次重点修正】根据指定的类型代码（lov_name）精确关联，将“IIT分类”、“注射剂名称”等字段从代码转换为对应的中文名称。
        -- 4. 增加了对领药申请单（exp_claim_header）的状态过滤，排除了已删除的单据。

        SELECT
            c.company_name AS '公司名称',
            h_drug_req.document_num AS 'receiveSysNumber',-- 领药系统单号
            u.full_name AS 'submitter', -- 领药申请人

            -- IIT立项申请单信息
            h_iit_req.document_num AS 'IIT立项申请单号',
            DATE_FORMAT(h_iit_req.submit_date, '%Y-%m-%d') AS 'submitDate', -- 提交日期
            iit_class_tl.value_meaning  AS 'iitClass', -- IIT分类
            h_iit_req.column46 AS 'projectNumber', -- 项目号

            -- 单据状态信息
            h_drug_req.status AS 'status', -- 原始状态代码
            CASE h_drug_req.status
                WHEN 'DRAFT' THEN '草稿'
                WHEN 'SUBMIT' THEN '已提交'
                WHEN 'APPROVE' THEN '已审批'
                WHEN 'REJECT' THEN '已驳回'
                WHEN 'CANCEL' THEN '已取消'
                WHEN 'CLOSE' THEN '已关闭'
                ELSE h_drug_req.status
            END AS 'statusName', -- 状态中文名称

            -- 立项部门层级
#             submit_dept_l1_tl.department_name AS 'setupDepartment',
#             submit_dept_l2_tl.department_name AS 'setupDepartment2',
#             submit_dept_l3_tl.department_name AS 'setupDepartment3',

            -- 【问题一修正】费用承担部门层级
            cost_center_l1_tl.department_name AS 'chargeDepartment',
            cost_center_l2_tl.department_name AS 'chargeDepartment2',
            cost_center_l3_tl.department_name AS 'chargeDepartment3',

            -- 【问题二修正】药品与物流信息
            injection_tl.value_meaning AS 'injectionName', -- 注射剂名称
            l.quantity AS 'injectionNum', -- 赠药数量
            SUM(l.quantity) OVER (PARTITION BY h_drug_req.header_id) AS 'totalNum', -- 总申请数量
            iit_center_tl.value_meaning AS 'iitCenter', -- IIT中心
            l.column16 AS 'researchDepartment', -- 科研科室
            l.column_json->>'$.column122' AS 'receiver', -- 收货人
            l.column_json->>'$.column110' AS 'receiverPhone', -- 收货人联系电话
            l.column_json->>'$.column109' AS 'receiveAddress', -- 收货地址
            sign_dept_tl.department_name AS 'signDepartment' -- 签收部门
        ,h_iit_req.submit_department as 'submitDepartmentId'
        FROM
        -- 基础表
        exp_claim_header AS h_drug_req
        JOIN exp_header_type AS ht_drug ON h_drug_req.header_type_id = ht_drug.type_id AND ht_drug.type_code = 'YXIIT02'
        JOIN exp_claim_line AS l ON h_drug_req.header_id = l.header_id AND h_drug_req.company_id = l.company_id
        JOIN fnd_user AS u ON h_drug_req.submit_user = u.user_id
        JOIN fnd_company AS c ON h_drug_req.company_id = c.company_id

        -- 关联到源头“IIT立项申请单”
        LEFT JOIN exp_claim_header AS h_iit_req ON h_drug_req.link_header_id = h_iit_req.header_id AND h_drug_req.company_id = h_iit_req.company_id

        -- 构建“提交部门”的层级关系
#         LEFT JOIN fnd_department AS submit_dept_l1 ON h_iit_req.submit_department = submit_dept_l1.department_id
#         LEFT JOIN fnd_department_tl AS submit_dept_l1_tl ON submit_dept_l1.department_id = submit_dept_l1_tl.department_id AND submit_dept_l1_tl.language = 'zh_CN'
#         LEFT JOIN fnd_department AS submit_dept_l2 ON submit_dept_l1.supervisor_id = submit_dept_l2.department_id
#         LEFT JOIN fnd_department_tl AS submit_dept_l2_tl ON submit_dept_l2.department_id = submit_dept_l2_tl.department_id AND submit_dept_l2_tl.language = 'zh_CN'
#         LEFT JOIN fnd_department AS submit_dept_l3 ON submit_dept_l2.supervisor_id = submit_dept_l3.department_id
#         LEFT JOIN fnd_department_tl AS submit_dept_l3_tl ON submit_dept_l3.department_id = submit_dept_l3_tl.department_id AND submit_dept_l3_tl.language = 'zh_CN'

        -- 构建“费用承担部门”的层级关系
        LEFT JOIN fnd_department AS cost_center_l1 ON l.cost_center_id = cost_center_l1.department_id
        LEFT JOIN fnd_department_tl AS cost_center_l1_tl ON cost_center_l1.department_id = cost_center_l1_tl.department_id AND cost_center_l1_tl.language = 'zh_CN'
        LEFT JOIN fnd_department AS cost_center_l2 ON cost_center_l1.supervisor_id = cost_center_l2.department_id
        LEFT JOIN fnd_department_tl AS cost_center_l2_tl ON cost_center_l2.department_id = cost_center_l2_tl.department_id AND cost_center_l2_tl.language = 'zh_CN'
        LEFT JOIN fnd_department AS cost_center_l3 ON cost_center_l2.supervisor_id = cost_center_l3.department_id
        LEFT JOIN fnd_department_tl AS cost_center_l3_tl ON cost_center_l3.department_id = cost_center_l3_tl.department_id AND cost_center_l3_tl.language = 'zh_CN'

        -- 【修正关联逻辑】通过类型代码(lov_name)进行精确关联，将ID或代码转换为中文名称
        LEFT JOIN fnd_department_tl AS sign_dept_tl ON l.column34 = sign_dept_tl.department_id AND sign_dept_tl.language = 'zh_CN'

        LEFT JOIN fnd_lov AS iit_center_lov ON iit_center_lov.lov_name = 'ZD01'
        LEFT JOIN fnd_lov_value AS iit_center_v ON iit_center_lov.lov_id = iit_center_v.lov_id AND l.column42 = iit_center_v.value_code
        LEFT JOIN fnd_lov_value_tl AS iit_center_tl ON iit_center_v.value_id = iit_center_tl.value_id AND iit_center_tl.language = 'zh_CN'

        LEFT JOIN fnd_lov AS iit_class_lov ON iit_class_lov.lov_name = 'categories'
        LEFT JOIN fnd_lov_value AS iit_class_v ON iit_class_lov.lov_id = iit_class_v.lov_id AND h_iit_req.column15 = iit_class_v.value_code
        LEFT JOIN fnd_lov_value_tl AS iit_class_tl ON iit_class_v.value_id = iit_class_tl.value_id AND iit_class_tl.language = 'zh_CN'

        LEFT JOIN fnd_lov AS injection_lov ON injection_lov.lov_name = 'Payment_recipient'
        LEFT JOIN fnd_lov_value AS injection_v ON injection_lov.lov_id = injection_v.lov_id AND l.column_json->>'$.column100' = injection_v.value_code
        LEFT JOIN fnd_lov_value_tl AS injection_tl ON injection_v.value_id = injection_tl.value_id AND injection_tl.language = 'zh_CN'

        WHERE
        h_drug_req.status != 'deleted' -- 过滤掉已删除的领药申请单

        <!-- 动态条件：项目号过滤 -->
        <if test="query.projectNumber != null and query.projectNumber != ''">
            AND h_iit_req.column46 LIKE CONCAT('%', #{query.projectNumber}, '%')
        </if>

        <!-- 动态条件：IIT分类过滤 -->
        <if test="query.iitClass != null and query.iitClass != ''">
            AND iit_class_tl.value_meaning LIKE CONCAT('%', #{query.iitClass}, '%')
        </if>

        <!-- 动态条件：IIT中心过滤 -->
        <if test="query.iitCenter != null and query.iitCenter != ''">
            AND iit_center_tl.value_meaning LIKE CONCAT('%', #{query.iitCenter}, '%')
        </if>

        <!-- 动态条件：单据状态过滤（支持多选） -->
        <if test="query.statusList != null and query.statusList.size() > 0">
            AND h_drug_req.status IN
            <foreach collection="query.statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <!-- 动态条件：单据状态过滤（单选，兼容性保留） -->
        <if test="query.status != null and query.status != '' and (query.statusList == null or query.statusList.size() == 0)">
            AND h_drug_req.status = #{query.status}
        </if>

        ORDER BY
        c.company_name,
        h_drug_req.document_num
    </select>
</mapper>

