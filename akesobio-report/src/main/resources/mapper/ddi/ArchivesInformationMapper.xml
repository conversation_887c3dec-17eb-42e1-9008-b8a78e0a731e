<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.ArchivesInformationMapper">

    <resultMap type="ArchivesInformation" id="ArchivesInformationResult">
        <result property="id" column="id"/>
        <result property="institutionalCode" column="institutionalCode"/>
        <result property="institutionalName" column="institutionalName"/>
        <result property="institutionalAlias" column="institutionalAlias"/>
        <result property="registeredAddress" column="registeredAddress"/>
        <result property="registeredProvince" column="registeredProvince"/>
        <result property="registeredCity" column="registeredCity"/>
        <result property="registeredDistrict" column="registeredDistrict"/>
        <result property="secondaryDepartment" column="secondaryDepartment"/>
        <result property="thirdLevelDepartments" column="thirdLevelDepartments"/>
        <result property="fourthLevelDepartment" column="fourthLevelDepartment"/>
        <result property="fifthLevelDepartment" column="fifthLevelDepartment"/>
        <result property="firstLevelAttribute" column="firstLevelAttribute"/>
        <result property="unifiedSocialCreditCode" column="unifiedSocialCreditCode"/>
        <result property="responsiblePerson" column="responsiblePerson"/>
        <result property="variety1" column="variety1"/>
        <result property="unitPrice1" column="unitPrice1"/>
        <result property="variety2" column="variety2"/>
        <result property="unitPrice2" column="unitPrice2"/>
        <result property="variety3" column="variety3"/>
        <result property="unitPrice3" column="unitPrice3"/>
    </resultMap>

    <sql id="selectArchivesInformationVo">
        select id,
               institutionalCode,
               institutionalName,
               institutionalAlias,
               registeredAddress,
               registeredProvince,
               registeredCity,
               registeredDistrict,
               secondaryDepartment,
               thirdLevelDepartments,
               fourthLevelDepartment,
               fifthLevelDepartment,
               firstLevelAttribute,
               unifiedSocialCreditCode,
               responsiblePerson,
               variety1,
               unitPrice1,
               variety2,
               unitPrice2,
               variety3,
               unitPrice3
        from archives_information
    </sql>

    <select id="selectArchivesInformationList" parameterType="ArchivesInformation"
            resultMap="ArchivesInformationResult">
        <include refid="selectArchivesInformationVo"/>
        <where>
            <if test="institutionalCode != null  and institutionalCode != ''">and institutionalCode like concat('%',
                #{institutionalCode}, '%')
            </if>
            <if test="institutionalName != null  and institutionalName != ''">and institutionalName like concat('%',
                #{institutionalName}, '%')
            </if>
            <if test="institutionalAlias != null  and institutionalAlias != ''">and institutionalAlias like concat('%',
                #{institutionalAlias}, '%')
            </if>
            <if test="registeredAddress != null  and registeredAddress != ''">and registeredAddress like concat('%',
                #{registeredAddress}, '%')
            </if>
            <if test="registeredProvince != null  and registeredProvince != ''">and registeredProvince like concat('%',
                #{registeredProvince}, '%')
            </if>
            <if test="registeredCity != null  and registeredCity != ''">and registeredCity like concat('%',
                #{registeredCity}, '%')
            </if>
            <if test="registeredDistrict != null  and registeredDistrict != ''">and registeredDistrict like concat('%',
                #{registeredDistrict}, '%')
            </if>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment like
                concat('%', #{secondaryDepartment}, '%')
            </if>
            <if test="thirdLevelDepartments != null  and thirdLevelDepartments != ''">and thirdLevelDepartments like
                concat('%', #{thirdLevelDepartments}, '%')
            </if>
            <if test="fourthLevelDepartment != null  and fourthLevelDepartment != ''">and fourthLevelDepartment like
                concat('%', #{fourthLevelDepartment}, '%')
            </if>
            <if test="fifthLevelDepartment != null  and fifthLevelDepartment != ''">and fifthLevelDepartment like
                concat('%', #{fifthLevelDepartment}, '%')
            </if>
            <if test="firstLevelAttribute != null  and firstLevelAttribute != ''">and firstLevelAttribute like
                concat('%', #{firstLevelAttribute}, '%')
            </if>
            <if test="unifiedSocialCreditCode != null  and unifiedSocialCreditCode != ''">and unifiedSocialCreditCode
                like concat('%', #{unifiedSocialCreditCode}, '%')
            </if>
            <if test="responsiblePerson != null  and responsiblePerson != ''">and responsiblePerson like concat('%',
                #{responsiblePerson}, '%')
            </if>
            <if test="provinceList !=null and provinceList.size>0">
                and registeredProvince in
                <foreach collection="provinceList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectArchivesInformationById" parameterType="Integer" resultMap="ArchivesInformationResult">
        <include refid="selectArchivesInformationVo"/>
        where id = #{id}
    </select>

    <insert id="insertArchivesInformation" parameterType="ArchivesInformation">
        insert into archives_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="institutionalCode != null">institutionalCode,</if>
            <if test="institutionalName != null">institutionalName,</if>
            <if test="institutionalAlias != null">institutionalAlias,</if>
            <if test="registeredAddress != null">registeredAddress,</if>
            <if test="registeredProvince != null">registeredProvince,</if>
            <if test="registeredCity != null">registeredCity,</if>
            <if test="registeredDistrict != null">registeredDistrict,</if>
            <if test="secondaryDepartment != null">secondaryDepartment,</if>
            <if test="thirdLevelDepartments != null">thirdLevelDepartments,</if>
            <if test="fourthLevelDepartment != null">fourthLevelDepartment,</if>
            <if test="fifthLevelDepartment != null">fifthLevelDepartment,</if>
            <if test="firstLevelAttribute != null">firstLevelAttribute,</if>
            <if test="unifiedSocialCreditCode != null">unifiedSocialCreditCode,</if>
            <if test="responsiblePerson != null">responsiblePerson,</if>
            <if test="variety1 != null">variety1,</if>
            <if test="unitPrice1 != null">unitPrice1,</if>
            <if test="variety2 != null">variety2,</if>
            <if test="unitPrice2 != null">unitPrice2,</if>
            <if test="variety3 != null">variety3,</if>
            <if test="unitPrice3 != null">unitPrice3,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="institutionalCode != null">#{institutionalCode},</if>
            <if test="institutionalName != null">#{institutionalName},</if>
            <if test="institutionalAlias != null">#{institutionalAlias},</if>
            <if test="registeredAddress != null">#{registeredAddress},</if>
            <if test="registeredProvince != null">#{registeredProvince},</if>
            <if test="registeredCity != null">#{registeredCity},</if>
            <if test="registeredDistrict != null">#{registeredDistrict},</if>
            <if test="secondaryDepartment != null">#{secondaryDepartment},</if>
            <if test="thirdLevelDepartments != null">#{thirdLevelDepartments},</if>
            <if test="fourthLevelDepartment != null">#{fourthLevelDepartment},</if>
            <if test="fifthLevelDepartment != null">#{fifthLevelDepartment},</if>
            <if test="firstLevelAttribute != null">#{firstLevelAttribute},</if>
            <if test="unifiedSocialCreditCode != null">#{unifiedSocialCreditCode},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="variety1 != null">#{variety1},</if>
            <if test="unitPrice1 != null">#{unitPrice1},</if>
            <if test="variety2 != null">#{variety2},</if>
            <if test="unitPrice2 != null">#{unitPrice2},</if>
            <if test="variety3 != null">#{variety3},</if>
            <if test="unitPrice3 != null">#{unitPrice3},</if>
        </trim>
    </insert>

    <update id="updateArchivesInformation" parameterType="ArchivesInformation">
        update archives_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="institutionalCode != null">institutionalCode = #{institutionalCode},</if>
            <if test="institutionalName != null">institutionalName = #{institutionalName},</if>
            <if test="institutionalAlias != null">institutionalAlias = #{institutionalAlias},</if>
            <if test="registeredAddress != null">registeredAddress = #{registeredAddress},</if>
            <if test="registeredProvince != null">registeredProvince = #{registeredProvince},</if>
            <if test="registeredCity != null">registeredCity = #{registeredCity},</if>
            <if test="registeredDistrict != null">registeredDistrict = #{registeredDistrict},</if>
            <if test="secondaryDepartment != null">secondaryDepartment = #{secondaryDepartment},</if>
            <if test="thirdLevelDepartments != null">thirdLevelDepartments = #{thirdLevelDepartments},</if>
            <if test="fourthLevelDepartment != null">fourthLevelDepartment = #{fourthLevelDepartment},</if>
            <if test="fifthLevelDepartment != null">fifthLevelDepartment = #{fifthLevelDepartment},</if>
            <if test="firstLevelAttribute != null">firstLevelAttribute = #{firstLevelAttribute},</if>
            <if test="unifiedSocialCreditCode != null">unifiedSocialCreditCode = #{unifiedSocialCreditCode},</if>
            <if test="responsiblePerson != null">responsiblePerson = #{responsiblePerson},</if>
            <if test="variety1 != null">variety1 = #{variety1},</if>
            <if test="unitPrice1 != null">unitPrice1 = #{unitPrice1},</if>
            <if test="variety2 != null">variety2 = #{variety2},</if>
            <if test="unitPrice2 != null">unitPrice2 = #{unitPrice2},</if>
            <if test="variety3 != null">variety3 = #{variety3},</if>
            <if test="unitPrice3 != null">unitPrice3 = #{unitPrice3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteArchivesInformationById" parameterType="Integer">
        delete
        from archives_information
        where id = #{id}
    </delete>

    <delete id="deleteArchivesInformationByIds" parameterType="String">
        delete from archives_information where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>