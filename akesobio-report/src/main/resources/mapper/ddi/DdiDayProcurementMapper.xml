<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.DdiDayProcurementMapper">

    <resultMap type="DdiDayProcurement" id="DdiDayProcurementResult">
        <result property="id" column="id"/>
        <result property="executionMonth" column="execution_month"/>
        <result property="businessMonth" column="business_month"/>
        <result property="businessCoding" column="business_coding"/>
        <result property="businessName" column="business_name"/>
        <result property="purchaseDate" column="purchase_date"/>
        <result property="vendorCode" column="vendor_code"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="standardProductCodes" column="standard_product_codes"/>
        <result property="standardProductName" column="standard_product_name"/>
        <result property="standardProductSpecification" column="standard_product_specification"/>
        <result property="standardUnits" column="standard_units"/>
        <result property="standardQuantity" column="standard_quantity"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="amount" column="amount"/>
        <result property="standardLotNumber" column="standard_lot_number"/>
        <result property="upstreamVendorCode" column="upstream_vendor_code"/>
        <result property="upstreamVendorName" column="upstream_vendor_name"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="productSpecifications" column="product_specifications"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="quantityUnits" column="quantity_units"/>
        <result property="quantity" column="quantity"/>
        <result property="productUnitPrice" column="product_unit_price"/>
        <result property="productAmount" column="product_amount"/>
        <result property="productLotNumber" column="product_lot_number"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="dataType" column="data_type"/>
        <result property="maintenanceStatus" column="maintenance_status"/>
        <result property="creationTime" column="creation_time"/>
        <result property="updated" column="updated"/>
        <result property="deletionTime" column="deletion_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="commercialGrade" column="commercialGrade"/>
        <result property="commercialProvince" column="commercialProvince"/>
    </resultMap>

    <sql id="selectDdiDayProcurementVo">
        select id,
               execution_month,
               business_month,
               business_coding,
               business_name,
               purchase_date,
               vendor_code,
               vendor_name,
               standard_product_codes,
               standard_product_name,
               standard_product_specification,
               standard_units,
               standard_quantity,
               unit_price,
               amount,
               standard_lot_number,
               upstream_vendor_code,
               upstream_vendor_name,
               product_code,
               product_name,
               product_specifications,
               manufacturer,
               quantity_units,
               quantity,
               product_unit_price,
               product_amount,
               product_lot_number,
               expiration_date,
               data_type,
               maintenance_status,
               creation_time,
               updated,
               deletion_time,
               delete_status
        from ddi_day_procurement
    </sql>

    <select id="selectDdiDayProcurementList" parameterType="DdiDayProcurement" resultMap="DdiDayProcurementResult">
        select
        a.*,
        b.customerName,
        b.commercialGrade,
        b.commercialProvince,
        b.businessRepresentative,
        b.businessDistrict,
        b.businessManager,
        b.businessRegion,
        b.businessRegionManage,
        b.commercialDirector
        from
        ddi_day_procurement a
        left join ddi_customer_information b ON b.customerCode= a.business_coding
        <where>
            <if test="executionMonth != null  and executionMonth != ''">and execution_month like concat('%',
                #{executionMonth}, '%')
            </if>
            <if test="businessMonth != null  and businessMonth != ''">and business_month like concat('%',
                #{businessMonth}, '%')
            </if>
            <if test="businessCoding != null  and businessCoding != ''">and business_coding like concat('%',
                #{businessCoding}, '%')
            </if>
            <if test="businessName != null  and businessName != ''">and business_name like concat('%', #{businessName},
                '%')
            </if>
            <if test="params.beginPurchaseDate != null and params.beginPurchaseDate != '' and params.endPurchaseDate != null and params.endPurchaseDate != ''">
                and purchase_date between #{params.beginPurchaseDate} and #{params.endPurchaseDate}
            </if>
            <if test="vendorCode != null  and vendorCode != ''">and vendor_code like concat('%', #{vendorCode}, '%')
            </if>
            <if test="vendorName != null  and vendorName != ''">and vendor_name like concat('%', #{vendorName}, '%')
            </if>
            <if test="standardProductCodes != null  and standardProductCodes != ''">and standard_product_codes like
                concat('%', #{standardProductCodes}, '%')
            </if>
            <if test="standardProductName != null  and standardProductName != ''">and standard_product_name like
                concat('%', #{standardProductName}, '%')
            </if>
            <if test="standardProductSpecification != null  and standardProductSpecification != ''">and
                standard_product_specification like concat('%', #{standardProductSpecification}, '%')
            </if>
            <if test="standardUnits != null  and standardUnits != ''">and standard_units like concat('%',
                #{standardUnits}, '%')
            </if>
            <if test="standardQuantity != null ">and standard_quantity = #{standardQuantity}</if>
            <if test="unitPrice != null ">and unit_price = #{unitPrice}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="standardLotNumber != null  and standardLotNumber != ''">and standard_lot_number like concat('%',
                #{standardLotNumber}, '%')
            </if>
            <if test="upstreamVendorCode != null  and upstreamVendorCode != ''">and upstream_vendor_code like
                concat('%', #{upstreamVendorCode}, '%')
            </if>
            <if test="upstreamVendorName != null  and upstreamVendorName != ''">and upstream_vendor_name like
                concat('%', #{upstreamVendorName}, '%')
            </if>
            <if test="productCode != null  and productCode != ''">and product_code like concat('%', #{productCode},
                '%')
            </if>
            <if test="productName != null  and productName != ''">and product_name like concat('%', #{productName},
                '%')
            </if>
            <if test="productSpecifications != null  and productSpecifications != ''">and product_specifications like
                concat('%', #{productSpecifications}, '%')
            </if>
            <if test="manufacturer != null  and manufacturer != ''">and manufacturer like concat('%', #{manufacturer},
                '%')
            </if>
            <if test="quantityUnits != null  and quantityUnits != ''">and quantity_units = #{quantityUnits}</if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="productUnitPrice != null ">and product_unit_price = #{productUnitPrice}</if>
            <if test="productAmount != null ">and product_amount = #{productAmount}</if>
            <if test="productLotNumber != null  and productLotNumber != ''">and product_lot_number like concat('%',
                #{productLotNumber}, '%')
            </if>
            <if test="params.beginExpirationDate != null and params.beginExpirationDate != '' and params.endExpirationDate != null and params.endExpirationDate != ''">
                and expiration_date between #{params.beginExpirationDate} and #{params.endExpirationDate}
            </if>
            <if test="dataType != null  and dataType != ''">and data_type = #{dataType}</if>
            <if test="maintenanceStatus != null  and maintenanceStatus != ''">and maintenance_status like concat('%',
                #{maintenanceStatus}, '%')
            </if>
            <if test="creationTime != null ">and creation_time = #{creationTime}</if>
            <if test="updated != null ">and updated = #{updated}</if>
            <if test="deletionTime != null ">and deletion_time = #{deletionTime}</if>
            <if test="deleteStatus != null ">and delete_status = #{deleteStatus}</if>
            <if test="commercialGrade != null  and commercialGrade != ''">and commercialGrade like concat('%',
                #{commercialGrade}, '%')
            </if>
            <if test="commercialProvince != null  and commercialProvince != ''">and commercialProvince like concat('%',
                #{commercialProvince}, '%')
            </if>
            <if test="provinceList !=null and provinceList.size>0">
                and commercialProvince in
                <foreach collection="provinceList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and a.delete_status ='0' and b.commercialGrade in ('一级商','二级商')
        </where>
    </select>

</mapper>