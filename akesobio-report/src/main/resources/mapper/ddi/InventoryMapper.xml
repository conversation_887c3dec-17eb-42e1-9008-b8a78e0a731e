<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.InventoryMapper">

    <resultMap type="Inventory" id="InventoryResult">
        <result property="id" column="id"/>
        <result property="salesArea" column="salesArea"/>
        <result property="salesRegion" column="salesRegion"/>
        <result property="salesProvince" column="salesProvince"/>
        <result property="salesDate" column="salesDate"/>
        <result property="business" column="business"/>
        <result property="pharmacy" column="pharmacy"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="productName" column="productName"/>
        <result property="productCode" column="productCode"/>
    </resultMap>

    <sql id="selectInventoryVo">
        select id,
               salesArea,
               salesRegion,
               salesProvince,
               salesDate,
               business,
               pharmacy,
               deleteStatus,
               productName,
               productCode
        from inventory
    </sql>

    <select id="selectInventoryList" parameterType="Inventory" resultMap="InventoryResult">
        <include refid="selectInventoryVo"/>
        <where>
            <if test="salesArea != null  and salesArea != ''">and salesArea like concat('%', #{salesArea}, '%')</if>
            <if test="salesRegion != null  and salesRegion != ''">and salesRegion like concat('%', #{salesRegion},
                '%')
            </if>
            <if test="salesProvince != null  and salesProvince != ''">and salesProvince like concat('%',
                #{salesProvince}, '%')
            </if>
            <if test="salesDate != null ">and salesDate = #{salesDate}</if>
            <if test="business != null ">and business = #{business}</if>
            <if test="pharmacy != null ">and pharmacy = #{pharmacy}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="productCode != null  and productCode != ''">and productCode like concat('%', #{productCode},
                '%')
            </if>
            <if test="provinceList !=null and provinceList.size>0">
                and salesProvince in
                <foreach collection="provinceList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and deleteStatus = '0'
        </where>
    </select>

    <select id="selectInventoryById" parameterType="Integer" resultMap="InventoryResult">
        <include refid="selectInventoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertInventory" parameterType="Inventory">
        insert into inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="salesArea != null">salesArea,</if>
            <if test="salesRegion != null">salesRegion,</if>
            <if test="salesProvince != null">salesProvince,</if>
            <if test="salesDate != null">salesDate,</if>
            <if test="business != null">business,</if>
            <if test="pharmacy != null">pharmacy,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="productName != null">productName,</if>
            <if test="productCode != null">productCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="salesArea != null">#{salesArea},</if>
            <if test="salesRegion != null">#{salesRegion},</if>
            <if test="salesProvince != null">#{salesProvince},</if>
            <if test="salesDate != null">#{salesDate},</if>
            <if test="business != null">#{business},</if>
            <if test="pharmacy != null">#{pharmacy},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productCode != null">#{productCode},</if>
        </trim>
    </insert>

    <update id="updateInventory" parameterType="Inventory">
        update inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="salesArea != null">salesArea = #{salesArea},</if>
            <if test="salesRegion != null">salesRegion = #{salesRegion},</if>
            <if test="salesProvince != null">salesProvince = #{salesProvince},</if>
            <if test="salesDate != null">salesDate = #{salesDate},</if>
            <if test="business != null">business = #{business},</if>
            <if test="pharmacy != null">pharmacy = #{pharmacy},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="productName != null">productName = #{productName},</if>
            <if test="productCode != null">productCode = #{productCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryById" parameterType="Integer">
        delete
        from inventory
        where id = #{id}
    </delete>

    <delete id="deleteInventoryByIds" parameterType="String">
        delete from inventory where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateInventoryStatus" parameterType="Inventory">
        update inventory
        set deleteStatus='1'
        where salesDate = #{salesDate}
    </update>
</mapper>