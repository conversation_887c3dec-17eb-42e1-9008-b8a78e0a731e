<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.InventoryReportMapper">

    <resultMap type="InventoryReport" id="InventoryReportResult">
        <result property="id" column="id"/>
        <result property="secondaryDepartment" column="secondaryDepartment"/>
        <result property="thirdLevelDepartments" column="thirdLevelDepartments"/>
        <result property="fourthLevelDepartment" column="fourthLevelDepartment"/>
        <result property="commercialProvince" column="commercialProvince"/>
        <result property="commercialGrade" column="commercialGrade"/>
        <result property="businessRepresentative" column="businessRepresentative"/>
        <result property="customerCode" column="customerCode"/>
        <result property="customerName" column="customerName"/>
        <result property="productCode" column="productCode"/>
        <result property="productName" column="productName"/>
        <result property="openingBalance" column="openingBalance"/>
        <result property="purchaseQuantity" column="purchaseQuantity"/>
        <result property="purchaseOther" column="purchaseOther"/>
        <result property="purchaseSummary" column="purchaseSummary"/>
        <result property="salesQuantity" column="salesQuantity"/>
        <result property="salesOthers" column="salesOthers"/>
        <result property="salesSummary" column="salesSummary"/>
        <result property="otherData" column="otherData"/>
        <result property="theoreticalStock" column="theoreticalStock"/>
        <result property="flowToInventory" column="flowToInventory"/>
        <result property="overdueSales" column="overdueSales"/>
        <result property="overduePurchase" column="overduePurchase"/>
        <result property="closingBalance" column="closingBalance"/>
        <result property="inventoryStatus" column="inventoryStatus"/>
        <result property="reason" column="reason"/>
        <result property="verification" column="verification"/>
        <result property="remarks" column="remarks"/>
        <result property="yearMonth" column="yearMonth"/>
        <result property="marketableDays" column="marketableDays"/>
        <result property="daysChange" column="daysChange"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="purchaseVerification" column="purchaseVerification"/>
    </resultMap>

    <sql id="selectInventoryReportVo">
        select id,
               secondaryDepartment,
               thirdLevelDepartments,
               fourthLevelDepartment,
               commercialProvince,
               commercialGrade,
               businessRepresentative,
               customerCode,
               customerName,
               productCode,
               productName,
               openingBalance,
               purchaseQuantity,
               purchaseOther,
               purchaseSummary,
               salesQuantity,
               salesOthers,
               salesSummary,
               otherData,
               theoreticalStock,
               flowToInventory,
               overdueSales,
               overduePurchase,
               closingBalance,
               inventoryStatus,
               reason,
               verification,
               remarks,
               yearMonth,
               marketableDays,
               daysChange,
               deleteStatus,
               purchaseVerification
        from inventory_report
    </sql>

    <select id="selectInventoryReportList" parameterType="InventoryReport" resultMap="InventoryReportResult">
        <include refid="selectInventoryReportVo"/>
        <where>
            <if test="secondaryDepartment != null  and secondaryDepartment != ''">and secondaryDepartment like
                concat('%', #{secondaryDepartment}, '%')
            </if>
            <if test="thirdLevelDepartments != null  and thirdLevelDepartments != ''">and thirdLevelDepartments like
                concat('%', #{thirdLevelDepartments}, '%')
            </if>
            <if test="fourthLevelDepartment != null  and fourthLevelDepartment != ''">and fourthLevelDepartment like
                concat('%', #{fourthLevelDepartment}, '%')
            </if>
            <if test="commercialProvince != null  and commercialProvince != ''">and commercialProvince like concat('%',
                #{commercialProvince}, '%')
            </if>
            <if test="commercialGrade != null  and commercialGrade != ''">and commercialGrade like concat('%',
                #{commercialGrade}, '%')
            </if>
            <if test="businessRepresentative != null  and businessRepresentative != ''">and businessRepresentative like
                concat('%', #{businessRepresentative}, '%')
            </if>
            <if test="customerCode != null  and customerCode != ''">and customerCode like concat('%', #{customerCode},
                '%')
            </if>
            <if test="customerName != null  and customerName != ''">and customerName like concat('%', #{customerName},
                '%')
            </if>
            <if test="productCode != null  and productCode != ''">and productCode like concat('%', #{productCode},
                '%')
            </if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="openingBalance != null ">and openingBalance = #{openingBalance}</if>
            <if test="purchaseQuantity != null ">and purchaseQuantity = #{purchaseQuantity}</if>
            <if test="purchaseOther != null ">and purchaseOther = #{purchaseOther}</if>
            <if test="purchaseSummary != null ">and purchaseSummary = #{purchaseSummary}</if>
            <if test="salesQuantity != null ">and salesQuantity = #{salesQuantity}</if>
            <if test="salesOthers != null ">and salesOthers = #{salesOthers}</if>
            <if test="salesSummary != null ">and salesSummary = #{salesSummary}</if>
            <if test="otherData != null  and otherData != ''">and otherData like concat('%', #{otherData}, '%')</if>
            <if test="theoreticalStock != null ">and theoreticalStock = #{theoreticalStock}</if>
            <if test="flowToInventory != null ">and flowToInventory = #{flowToInventory}</if>
            <if test="overdueSales != null ">and overdueSales = #{overdueSales}</if>
            <if test="overduePurchase != null ">and overduePurchase = #{overduePurchase}</if>
            <if test="closingBalance != null ">and closingBalance = #{closingBalance}</if>
            <if test="inventoryStatus != null  and inventoryStatus != ''">and inventoryStatus like concat('%',
                #{inventoryStatus}, '%')
            </if>
            <if test="reason != null  and reason != ''">and reason like concat('%', #{reason}, '%')</if>
            <if test="verification != null ">and verification = #{verification}</if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth like concat('%', #{yearMonth}, '%')</if>
            <if test="marketableDays != null ">and marketableDays = #{marketableDays}</if>
            <if test="daysChange != null ">and daysChange = #{daysChange}</if>
            <if test="provinceList !=null and provinceList.size>0">
                and commercialProvince in
                <foreach collection="provinceList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and commercialGrade in ('一级商','二级商') and deleteStatus='0'
        </where>
    </select>

    <select id="selectAllInventoryReportList" parameterType="InventoryReport" resultMap="InventoryReportResult">
        <include refid="selectInventoryReportVo"/>
        <where>
            <if test="customerCode != null  and customerCode != ''">and customerCode = #{customerCode}</if>
            <if test="productName != null  and productName != ''">and productName = #{productName}</if>
            <if test="yearMonth != null  and yearMonth != ''">and yearMonth = #{yearMonth}</if>
            and deleteStatus='0'
        </where>
    </select>


    <select id="selectInventoryReportById" parameterType="Integer" resultMap="InventoryReportResult">
        <include refid="selectInventoryReportVo"/>
        where id = #{id}
    </select>

    <insert id="insertInventoryReport" parameterType="InventoryReport">
        insert into inventory_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="secondaryDepartment != null">secondaryDepartment,</if>
            <if test="thirdLevelDepartments != null">thirdLevelDepartments,</if>
            <if test="fourthLevelDepartment != null">fourthLevelDepartment,</if>
            <if test="commercialProvince != null">commercialProvince,</if>
            <if test="commercialGrade != null">commercialGrade,</if>
            <if test="businessRepresentative != null">businessRepresentative,</if>
            <if test="customerCode != null">customerCode,</if>
            <if test="customerName != null">customerName,</if>
            <if test="productCode != null">productCode,</if>
            <if test="productName != null">productName,</if>
            <if test="openingBalance != null">openingBalance,</if>
            <if test="purchaseQuantity != null">purchaseQuantity,</if>
            <if test="purchaseOther != null">purchaseOther,</if>
            <if test="purchaseSummary != null">purchaseSummary,</if>
            <if test="salesQuantity != null">salesQuantity,</if>
            <if test="salesOthers != null">salesOthers,</if>
            <if test="salesSummary != null">salesSummary,</if>
            <if test="otherData != null">otherData,</if>
            <if test="theoreticalStock != null">theoreticalStock,</if>
            <if test="flowToInventory != null">flowToInventory,</if>
            <if test="overdueSales != null">overdueSales,</if>
            <if test="overduePurchase != null">overduePurchase,</if>
            <if test="closingBalance != null">closingBalance,</if>
            <if test="inventoryStatus != null">inventoryStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="verification != null">verification,</if>
            <if test="remarks != null">remarks,</if>
            <if test="yearMonth != null">yearMonth,</if>
            <if test="marketableDays != null">marketableDays,</if>
            <if test="daysChange != null">daysChange,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="purchaseVerification != null">purchaseVerification,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="secondaryDepartment != null">#{secondaryDepartment},</if>
            <if test="thirdLevelDepartments != null">#{thirdLevelDepartments},</if>
            <if test="fourthLevelDepartment != null">#{fourthLevelDepartment},</if>
            <if test="commercialProvince != null">#{commercialProvince},</if>
            <if test="commercialGrade != null">#{commercialGrade},</if>
            <if test="businessRepresentative != null">#{businessRepresentative},</if>
            <if test="customerCode != null">#{customerCode},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="productCode != null">#{productCode},</if>
            <if test="productName != null">#{productName},</if>
            <if test="openingBalance != null">#{openingBalance},</if>
            <if test="purchaseQuantity != null">#{purchaseQuantity},</if>
            <if test="purchaseOther != null">#{purchaseOther},</if>
            <if test="purchaseSummary != null">#{purchaseSummary},</if>
            <if test="salesQuantity != null">#{salesQuantity},</if>
            <if test="salesOthers != null">#{salesOthers},</if>
            <if test="salesSummary != null">#{salesSummary},</if>
            <if test="otherData != null">#{otherData},</if>
            <if test="theoreticalStock != null">#{theoreticalStock},</if>
            <if test="flowToInventory != null">#{flowToInventory},</if>
            <if test="overdueSales != null">#{overdueSales},</if>
            <if test="overduePurchase != null">#{overduePurchase},</if>
            <if test="closingBalance != null">#{closingBalance},</if>
            <if test="inventoryStatus != null">#{inventoryStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="verification != null">#{verification},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="marketableDays != null">#{marketableDays},</if>
            <if test="daysChange != null">#{daysChange},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="purchaseVerification != null">#{purchaseVerification},</if>
        </trim>
    </insert>

    <update id="updateInventoryReport" parameterType="InventoryReport">
        update inventory_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="secondaryDepartment != null">secondaryDepartment = #{secondaryDepartment},</if>
            <if test="thirdLevelDepartments != null">thirdLevelDepartments = #{thirdLevelDepartments},</if>
            <if test="fourthLevelDepartment != null">fourthLevelDepartment = #{fourthLevelDepartment},</if>
            <if test="commercialProvince != null">commercialProvince = #{commercialProvince},</if>
            <if test="commercialGrade != null">commercialGrade = #{commercialGrade},</if>
            <if test="businessRepresentative != null">businessRepresentative = #{businessRepresentative},</if>
            <if test="customerCode != null">customerCode = #{customerCode},</if>
            <if test="customerName != null">customerName = #{customerName},</if>
            <if test="productCode != null">productCode = #{productCode},</if>
            <if test="productName != null">productName = #{productName},</if>
            <if test="openingBalance != null">openingBalance = #{openingBalance},</if>
            <if test="purchaseQuantity != null">purchaseQuantity = #{purchaseQuantity},</if>
            <if test="purchaseOther != null">purchaseOther = #{purchaseOther},</if>
            <if test="purchaseSummary != null">purchaseSummary = #{purchaseSummary},</if>
            <if test="salesQuantity != null">salesQuantity = #{salesQuantity},</if>
            <if test="salesOthers != null">salesOthers = #{salesOthers},</if>
            <if test="salesSummary != null">salesSummary = #{salesSummary},</if>
            <if test="otherData != null">otherData = #{otherData},</if>
            <if test="theoreticalStock != null">theoreticalStock = #{theoreticalStock},</if>
            <if test="flowToInventory != null">flowToInventory = #{flowToInventory},</if>
            <if test="overdueSales != null">overdueSales = #{overdueSales},</if>
            <if test="overduePurchase != null">overduePurchase = #{overduePurchase},</if>
            <if test="closingBalance != null">closingBalance = #{closingBalance},</if>
            <if test="inventoryStatus != null">inventoryStatus = #{inventoryStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="verification != null">verification = #{verification},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="yearMonth != null">yearMonth = #{yearMonth},</if>
            <if test="marketableDays != null">marketableDays = #{marketableDays},</if>
            <if test="daysChange != null">daysChange = #{daysChange},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="purchaseVerification != null">purchaseVerification = #{purchaseVerification},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryReportById" parameterType="Integer">
        delete
        from inventory_report
        where id = #{id}
    </delete>

    <delete id="deleteInventoryReportByIds" parameterType="String">
        delete from inventory_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>