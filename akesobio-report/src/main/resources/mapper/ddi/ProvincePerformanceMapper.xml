<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.ProvincePerformanceMapper">

    <resultMap type="ProvincePerformance" id="ProvincePerformanceResult">
        <result property="id" column="id"/>
        <result property="salesArea" column="salesArea"/>
        <result property="salesRegion" column="salesRegion"/>
        <result property="salesProvince" column="salesProvince"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="hospitalPurchases" column="hospitalPurchases"/>
        <result property="dtpSales" column="dtpSales"/>
        <result property="deleteStatus" column="deleteStatus"/>
        <result property="hospitalPurchasesAmount" column="hospitalPurchasesAmount"/>
        <result property="dtpSalesAmount" column="dtpSalesAmount"/>
        <result property="calculateUnitPrice" column="calculateUnitPrice"/>
        <result property="productName" column="productName"/>
        <result property="productCode" column="productCode"/>
    </resultMap>

    <sql id="selectProvincePerformanceVo">
        select id,
               salesArea,
               salesRegion,
               salesProvince, year, month, hospitalPurchases, dtpSales, deleteStatus, hospitalPurchasesAmount, dtpSalesAmount, calculateUnitPrice, productName, productCode
        from province_performance
    </sql>

    <select id="selectProvincePerformanceList" parameterType="ProvincePerformance"
            resultMap="ProvincePerformanceResult">
        <include refid="selectProvincePerformanceVo"/>
        <where>
            <if test="salesArea != null  and salesArea != ''">and salesArea like concat('%', #{salesArea}, '%')</if>
            <if test="salesRegion != null  and salesRegion != ''">and salesRegion like concat('%', #{salesRegion},
                '%')
            </if>
            <if test="salesProvince != null  and salesProvince != ''">and salesProvince like concat('%',
                #{salesProvince}, '%')
            </if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null ">and month = #{month}</if>
            <if test="hospitalPurchases != null ">and hospitalPurchases = #{hospitalPurchases}</if>
            <if test="dtpSales != null ">and dtpSales = #{dtpSales}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
            <if test="hospitalPurchasesAmount != null ">and hospitalPurchasesAmount = #{hospitalPurchasesAmount}</if>
            <if test="dtpSalesAmount != null ">and dtpSalesAmount = #{dtpSalesAmount}</if>
            <if test="calculateUnitPrice != null ">and calculateUnitPrice = #{calculateUnitPrice}</if>
            <if test="productName != null  and productName != ''">and productName like concat('%', #{productName},
                '%')
            </if>
            <if test="productCode != null  and productCode != ''">and productCode like concat('%', #{productCode},
                '%')
            </if>
        </where>
    </select>

    <select id="selectProvincePerformanceById" parameterType="Integer" resultMap="ProvincePerformanceResult">
        <include refid="selectProvincePerformanceVo"/>
        where id = #{id}
    </select>

    <insert id="insertProvincePerformance" parameterType="ProvincePerformance">
        insert into province_performance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="salesArea != null">salesArea,</if>
            <if test="salesRegion != null">salesRegion,</if>
            <if test="salesProvince != null">salesProvince,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="hospitalPurchases != null">hospitalPurchases,</if>
            <if test="dtpSales != null">dtpSales,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
            <if test="hospitalPurchasesAmount != null">hospitalPurchasesAmount,</if>
            <if test="dtpSalesAmount != null">dtpSalesAmount,</if>
            <if test="calculateUnitPrice != null">calculateUnitPrice,</if>
            <if test="productName != null">productName,</if>
            <if test="productCode != null">productCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="salesArea != null">#{salesArea},</if>
            <if test="salesRegion != null">#{salesRegion},</if>
            <if test="salesProvince != null">#{salesProvince},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="hospitalPurchases != null">#{hospitalPurchases},</if>
            <if test="dtpSales != null">#{dtpSales},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="hospitalPurchasesAmount != null">#{hospitalPurchasesAmount},</if>
            <if test="dtpSalesAmount != null">#{dtpSalesAmount},</if>
            <if test="calculateUnitPrice != null">#{calculateUnitPrice},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productCode != null">#{productCode},</if>
        </trim>
    </insert>

    <update id="updateProvincePerformance" parameterType="ProvincePerformance">
        update province_performance
        <trim prefix="SET" suffixOverrides=",">
            <if test="salesArea != null">salesArea = #{salesArea},</if>
            <if test="salesRegion != null">salesRegion = #{salesRegion},</if>
            <if test="salesProvince != null">salesProvince = #{salesProvince},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="hospitalPurchases != null">hospitalPurchases = #{hospitalPurchases},</if>
            <if test="dtpSales != null">dtpSales = #{dtpSales},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
            <if test="hospitalPurchasesAmount != null">hospitalPurchasesAmount = #{hospitalPurchasesAmount},</if>
            <if test="dtpSalesAmount != null">dtpSalesAmount = #{dtpSalesAmount},</if>
            <if test="calculateUnitPrice != null">calculateUnitPrice = #{calculateUnitPrice},</if>
            <if test="productName != null">productName = #{productName},</if>
            <if test="productCode != null">productCode = #{productCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProvincePerformanceById" parameterType="Integer">
        delete
        from province_performance
        where id = #{id}
    </delete>

    <delete id="deleteProvincePerformanceByIds" parameterType="String">
        delete from province_performance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>