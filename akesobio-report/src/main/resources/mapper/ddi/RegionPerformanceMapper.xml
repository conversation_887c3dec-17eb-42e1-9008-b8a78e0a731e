<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.ddi.mapper.RegionPerformanceMapper">

    <resultMap type="RegionPerformance" id="RegionPerformanceResult">
        <result property="id" column="id"/>
        <result property="salesArea" column="salesArea"/>
        <result property="salesRegion" column="salesRegion"/>
        <result property="salesAddress" column="salesAddress"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="hospitalPurchases" column="hospitalPurchases"/>
        <result property="dtpSales" column="dtpSales"/>
        <result property="deleteStatus" column="deleteStatus"/>
    </resultMap>

    <sql id="selectRegionPerformanceVo">
        select id, salesArea, salesRegion, salesAddress, year, month, hospitalPurchases, dtpSales, deleteStatus
        from region_performance
    </sql>

    <select id="selectRegionPerformanceList" parameterType="RegionPerformance" resultMap="RegionPerformanceResult">
        <include refid="selectRegionPerformanceVo"/>
        <where>
            <if test="salesArea != null  and salesArea != ''">and salesArea like concat('%', #{salesArea}, '%')</if>
            <if test="salesRegion != null  and salesRegion != ''">and salesRegion like concat('%', #{salesRegion},
                '%')
            </if>
            <if test="salesAddress != null  and salesAddress != ''">and salesAddress like concat('%', #{salesAddress},
                '%')
            </if>
            <if test="year != null ">and year = #{year}</if>
            <if test="month != null ">and month = #{month}</if>
            <if test="hospitalPurchases != null ">and hospitalPurchases = #{hospitalPurchases}</if>
            <if test="dtpSales != null ">and dtpSales = #{dtpSales}</if>
            <if test="deleteStatus != null ">and deleteStatus = #{deleteStatus}</if>
        </where>
    </select>

    <select id="selectRegionPerformanceById" parameterType="Integer" resultMap="RegionPerformanceResult">
        <include refid="selectRegionPerformanceVo"/>
        where id = #{id}
    </select>

    <insert id="insertRegionPerformance" parameterType="RegionPerformance">
        insert into region_performance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="salesArea != null">salesArea,</if>
            <if test="salesRegion != null">salesRegion,</if>
            <if test="salesAddress != null">salesAddress,</if>
            <if test="year != null">year,</if>
            <if test="month != null">month,</if>
            <if test="hospitalPurchases != null">hospitalPurchases,</if>
            <if test="dtpSales != null">dtpSales,</if>
            <if test="deleteStatus != null">deleteStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="salesArea != null">#{salesArea},</if>
            <if test="salesRegion != null">#{salesRegion},</if>
            <if test="salesAddress != null">#{salesAddress},</if>
            <if test="year != null">#{year},</if>
            <if test="month != null">#{month},</if>
            <if test="hospitalPurchases != null">#{hospitalPurchases},</if>
            <if test="dtpSales != null">#{dtpSales},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
        </trim>
    </insert>

    <update id="updateRegionPerformance" parameterType="RegionPerformance">
        update region_performance
        <trim prefix="SET" suffixOverrides=",">
            <if test="salesArea != null">salesArea = #{salesArea},</if>
            <if test="salesRegion != null">salesRegion = #{salesRegion},</if>
            <if test="salesAddress != null">salesAddress = #{salesAddress},</if>
            <if test="year != null">year = #{year},</if>
            <if test="month != null">month = #{month},</if>
            <if test="hospitalPurchases != null">hospitalPurchases = #{hospitalPurchases},</if>
            <if test="dtpSales != null">dtpSales = #{dtpSales},</if>
            <if test="deleteStatus != null">deleteStatus = #{deleteStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRegionPerformanceById" parameterType="Integer">
        delete
        from region_performance
        where id = #{id}
    </delete>

    <delete id="deleteRegionPerformanceByIds" parameterType="String">
        delete from region_performance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>