<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.hr.mapper.ProcessControlContractMapper">

    <resultMap type="ProcessControlContract" id="ProcessControlContractResult">
    </resultMap>

    <!-- 流程管控 合同 -->
    <sql id="selectProcessControlContract">
        SELECT a.fd_id                                                                           AS id,
               a.fd_name                                                                         AS name,
               a.fd_staff_no                                                                     AS loginName,
               d.dep_name_all                                                                    AS department,
               i.fd_name                                                                         AS leaderName,
               c.fd_name                                                                         AS post,
               a.fd_entry_time                                                                   AS entryTime,
               a.fd_leave_time                                                                   AS leaveTime,
               (CASE a.fd_status
                    WHEN 'official' THEN '正式'
                    WHEN 'trial' THEN '试用'
                    WHEN 'practice' THEN '实习'
                    WHEN 'leave' THEN '离职'
                    ELSE '其他'
                   END)                                                                             status,
               (SELECT TOP 1 hr_staff_person_info.fd_name
                FROM hr_staff_person_exp_cont
                         LEFT JOIN hr_staff_person_info
                                   ON hr_staff_person_info.fd_id = hr_staff_person_exp_cont.fd_creator_id
                WHERE hr_staff_person_exp_cont.fd_person_info_id = a.fd_id
                ORDER BY hr_staff_person_exp_cont.fd_create_time DESC)                           AS handlePerson,
               (SELECT TOP 1 fd_handle_date
                FROM hr_staff_person_exp_cont
                WHERE fd_person_info_id = a.fd_id
                ORDER BY fd_create_time DESC)                                                    AS contractSignDate,
               (SELECT TOP 1 fd_begin_date
                FROM hr_staff_person_exp_cont
                WHERE fd_person_info_id = a.fd_id
                ORDER BY fd_create_time)                                                         AS firstBeginDate,
               (SELECT TOP 1 fd_end_date
                FROM hr_staff_person_exp_cont
                WHERE fd_person_info_id = a.fd_id
                ORDER BY fd_create_time)                                                         AS firstEndDate,
               (SELECT TOP 1 fd_begin_date
                FROM hr_staff_person_exp_cont
                WHERE fd_person_info_id = a.fd_id
                ORDER BY fd_create_time DESC)                                                    AS beginDate,
               (SELECT TOP 1 fd_end_date
                FROM hr_staff_person_exp_cont
                WHERE fd_person_info_id = a.fd_id
                ORDER BY fd_create_time DESC)                                                    AS endDate,
               (SELECT TOP 1 CASE WHEN fd_cont_type IS NULL THEN fd_name ELSE fd_cont_type
        END
                FROM hr_staff_person_exp_cont
                WHERE fd_person_info_id = a.fd_id
                ORDER BY fd_create_time DESC)                                                    AS contractType,
               (SELECT COUNT(*) FROM hr_staff_person_exp_cont WHERE fd_person_info_id = a.fd_id) AS num
        FROM hr_staff_person_info a
                 LEFT JOIN dept_name_all_a d ON d.fd_id = a.fd_id
                 LEFT JOIN sys_org_element i ON i.fd_id = a.fd_report_leader_id
                 LEFT JOIN hr_staff_person_post b ON b.fd_personid = a.fd_id
                 LEFT JOIN hr_org_element c ON c.fd_id = b.fd_postid
    </sql>
    <!-- 流程管控 合同 -->
    <select id="queryProcessControlContract" parameterType="ProcessControlContract"
            resultMap="ProcessControlContractResult">
        <include refid="selectProcessControlContract"/>
        <where>
            <if test="name != null and name != ''">
                AND (a.fd_name like concat('%',#{name},'%') OR a.fd_staff_no like concat('%',#{name},'%'))
            </if>
            <choose>
                <when test="status eq '在职'">
                    AND a.fd_status != 'leave'
                </when>
                <otherwise>
                    <if test="status != null  and status != ''">AND a.fd_status = #{status}</if>
                </otherwise>
            </choose>
        </where>
        ORDER BY a.fd_entry_time DESC
    </select>

    <!-- 流程管控 合同 2.0 -->
    <sql id="queryProcessControlContractnew">
        SELECT id,
               name,
               job_number      AS loginName,
               org_name        AS company,
               dept_name_all   AS department,
               leader_name     AS leaderName,
               post,
               job_grade       AS rank,
               entry_date      AS entryTime,
               leave_date      AS leaveTime,
               employee_status AS status,
               job_number_used_before AS jobNumberUsedBefore
        FROM hr_employee_info
    </sql>
    <!-- 流程管控 合同 2.0 -->
    <select id="queryProcessControlContractnew" parameterType="ProcessControlContract"
            resultMap="ProcessControlContractResult">
        <include refid="queryProcessControlContractnew"/>
        <where>
            job_number NOT IN (SELECT a.job_number_used_before FROM hr_employee_info a WHERE a.job_number_used_before IS NOT NULL)
            <if test="name != null and name != ''">
                AND (name like concat('%',#{name},'%') OR job_number like concat('%',#{name},'%'))
            </if>
            <if test="status != null and status != ''">AND employee_status like concat('%',#{status},'%')</if>
            <if test="company != null and company != ''">AND org_name like concat('%',#{company},'%')</if>
            <if test="department != null and department != ''">AND dept_name_all like concat('%',#{department},'%')</if>
            <if test="leaderName != null and leaderName != ''">AND leader_name like concat('%',#{leaderName},'%')</if>
        </where>
        ORDER BY entry_date DESC
    </select>

</mapper>