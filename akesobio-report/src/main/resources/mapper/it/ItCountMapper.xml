<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.it.mapper.ItCountMapper">
    
    <resultMap type="ItCount" id="ItCountResult">
        <result property="name"    column="name"    />
        <result property="jobNumber"    column="jobNumber"    />
        <result property="jobType1"    column="jobType1"    />
        <result property="jobType2"    column="jobType2"    />
        <result property="jobType3"    column="jobType3"    />
        <result property="jobType4"    column="jobType4"    />
        <result property="jobType5"    column="jobType5"    />
        <result property="jobType6"    column="jobType6"    />
        <result property="projectType1"    column="projectType1"    />
        <result property="projectType2"    column="projectType2"    />
        <result property="projectType3"    column="projectType3"    />
        <result property="projectType4"    column="projectType4"    />
        <result property="projectType5"    column="projectType5"    />
        <result property="projectType6"    column="projectType6"    />
        <result property="projectType7"    column="projectType7"    />
        <result property="projectType8"    column="projectType8"    />
        <result property="projectType9"    column="projectType9"    />
        <result property="projectType10"    column="projectType10"    />
        <result property="projectType11"    column="projectType11"    />
        <result property="projectType12"    column="projectType12"    />
        <result property="projectType13"    column="projectType13"    />
        <result property="projectType14"    column="projectType14"    />
        <result property="projectType15"    column="projectType15"    />
        <result property="projectType16"    column="projectType16"    />
        <result property="projectType17"    column="projectType17"    />
        <result property="projectType18"    column="projectType18"    />
        <result property="projectType19"    column="projectType19"    />
        <result property="projectType20"    column="projectType20"    />
        <result property="projectType21"    column="projectType21"    />
        <result property="projectType22"    column="projectType22"    />
        <result property="projectType23"    column="projectType23"    />
        <result property="projectType24"    column="projectType24"    />
        <result property="projectType25"    column="projectType25"    />
        <result property="projectType26"    column="projectType26"    />
        <result property="projectType27"    column="projectType27"    />
        <result property="projectType28"    column="projectType28"    />
        <result property="projectType29"    column="projectType29"    />
        <result property="projectType30"    column="projectType30"    />
        <result property="projectType31"    column="projectType31"    />
        <result property="projectType32"    column="projectType32"    />
        <result property="projectType33"    column="projectType33"    />
        <result property="projectType34"    column="projectType34"    />
        <result property="projectDetails1"    column="projectDetails1"    />
        <result property="projectDetails2"    column="projectDetails2"    />
        <result property="projectDetails3"    column="projectDetails3"    />
        <result property="projectDetails4"    column="projectDetails4"    />
        <result property="projectDetails5"    column="projectDetails5"    />
        <result property="projectDetails6"    column="projectDetails6"    />
        <result property="projectDetails7"    column="projectDetails7"    />
        <result property="projectDetails8"    column="projectDetails8"    />
        <result property="projectDetails9"    column="projectDetails9"    />
        <result property="projectDetails10"    column="projectDetails10"    />
        <result property="projectDetails11"    column="projectDetails11"    />
        <result property="projectDetails12"    column="projectDetails12"    />
        <result property="projectDetails13"    column="projectDetails13"    />
        <result property="projectDetails14"    column="projectDetails14"    />
        <result property="projectDetails15"    column="projectDetails15"    />
        <result property="projectDetails16"    column="projectDetails16"    />
        <result property="projectDetails17"    column="projectDetails17"    />
        <result property="projectDetails18"    column="projectDetails18"    />
        <result property="projectDetails19"    column="projectDetails19"    />
        <result property="projectDetails20"    column="projectDetails20"    />
        <result property="projectDetails21"    column="projectDetails21"    />
        <result property="projectDetails22"    column="projectDetails22"    />
        <result property="projectDetails23"    column="projectDetails23"    />
        <result property="projectDetails24"    column="projectDetails24"    />
        <result property="projectDetails25"    column="projectDetails25"    />
        <result property="projectDetails26"    column="projectDetails26"    />
        <result property="projectDetails27"    column="projectDetails27"    />
        <result property="projectDetails28"    column="projectDetails28"    />
        <result property="projectDetails29"    column="projectDetails29"    />
        <result property="projectDetails30"    column="projectDetails30"    />
        <result property="projectDetails31"    column="projectDetails31"    />
        <result property="projectDetails32"    column="projectDetails32"    />
        <result property="projectDetails33"    column="projectDetails33"    />
        <result property="projectDetails34"    column="projectDetails34"    />
        <result property="projectDetails35"    column="projectDetails35"    />
        <result property="projectDetails36"    column="projectDetails36"    />
        <result property="projectDetails37"    column="projectDetails37"    />
        <result property="projectDetails38"    column="projectDetails38"    />
        <result property="projectDetails39"    column="projectDetails39"    />
        <result property="projectDetails40"    column="projectDetails40"    />
        <result property="projectDetails41"    column="projectDetails41"    />
        <result property="projectDetails42"    column="projectDetails42"    />
        <result property="projectDetails43"    column="projectDetails43"    />
        <result property="projectDetails44"    column="projectDetails44"    />
        <result property="projectDetails45"    column="projectDetails45"    />
        <result property="projectDetails46"    column="projectDetails46"    />
        <result property="projectDetails47"    column="projectDetails47"    />
        <result property="projectDetails48"    column="projectDetails48"    />
        <result property="projectDetails49"    column="projectDetails49"    />
        <result property="projectDetails50"    column="projectDetails50"    />
        <result property="projectDetails51"    column="projectDetails51"    />
        <result property="projectDetails52"    column="projectDetails52"    />
        <result property="projectDetails53"    column="projectDetails53"    />
        <result property="projectDetails54"    column="projectDetails54"    />
        <result property="projectDetails55"    column="projectDetails55"    />
        <result property="projectDetails56"    column="projectDetails56"    />
        <result property="projectDetails57"    column="projectDetails57"    />
        <result property="projectDetails58"    column="projectDetails58"    />
        <result property="projectDetails59"    column="projectDetails59"    />
        <result property="projectDetails60"    column="projectDetails60"    />
        <result property="projectDetails61"    column="projectDetails61"    />
        <result property="projectDetails62"    column="projectDetails62"    />
        <result property="projectDetails63"    column="projectDetails63"    />
        <result property="projectDetails64"    column="projectDetails64"    />
        <result property="projectDetails65"    column="projectDetails65"    />
        <result property="projectDetails66"    column="projectDetails66"    />
        <result property="projectDetails67"    column="projectDetails67"    />
        <result property="projectDetails68"    column="projectDetails68"    />
        <result property="projectDetails69"    column="projectDetails69"    />
        <result property="projectDetails70"    column="projectDetails70"    />
        <result property="projectDetails71"    column="projectDetails71"    />
        <result property="projectDetails72"    column="projectDetails72"    />
        <result property="projectDetails73"    column="projectDetails73"    />
        <result property="projectDetails74"    column="projectDetails74"    />
        <result property="projectDetails75"    column="projectDetails75"    />
        <result property="projectDetails76"    column="projectDetails76"    />
        <result property="projectDetails77"    column="projectDetails77"    />
        <result property="projectDetails78"    column="projectDetails78"    />
        <result property="projectDetails79"    column="projectDetails79"    />
        <result property="projectDetails80"    column="projectDetails80"    />
        <result property="projectDetails81"    column="projectDetails81"    />
        <result property="projectDetails82"    column="projectDetails82"    />
        <result property="projectDetails83"    column="projectDetails83"    />
        <result property="projectDetails84"    column="projectDetails84"    />
        <result property="projectDetails85"    column="projectDetails85"    />
        <result property="projectDetails86"    column="projectDetails86"    />
        <result property="projectDetails87"    column="projectDetails87"    />
        <result property="projectDetails88"    column="projectDetails88"    />
        <result property="projectDetails89"    column="projectDetails89"    />
        <result property="projectDetails90"    column="projectDetails90"    />
        <result property="projectDetails91"    column="projectDetails91"    />
        <result property="projectDetails92"    column="projectDetails92"    />
        <result property="projectDetails93"    column="projectDetails93"    />
        <result property="projectDetails94"    column="projectDetails94"    />
    </resultMap>

    <sql id="selectItCountVo">
        select name, jobNumber, jobType1, jobType2, jobType3, jobType4, jobType5, jobType6, projectType1, projectType2, projectType3, projectType4, projectType5, projectType6, projectType7, projectType8, projectType9, projectType10, projectType11, projectType12, projectType13, projectType14, projectType15, projectType16, projectType17, projectType18, projectType19, projectType20, projectType21, projectType22, projectType23, projectType24, projectType25, projectType26, projectType27, projectType28, projectType29, projectType30, projectType31, projectType32, projectType33, projectType34, projectDetails1, projectDetails2, projectDetails3, projectDetails4, projectDetails5, projectDetails6, projectDetails7, projectDetails8, projectDetails9, projectDetails10, projectDetails11, projectDetails12, projectDetails13, projectDetails14, projectDetails15, projectDetails16, projectDetails17, projectDetails18, projectDetails19, projectDetails20, projectDetails21, projectDetails22, projectDetails23, projectDetails24, projectDetails25, projectDetails26, projectDetails27, projectDetails28, projectDetails29, projectDetails30, projectDetails31, projectDetails32, projectDetails33, projectDetails34, projectDetails35, projectDetails36, projectDetails37, projectDetails38, projectDetails39, projectDetails40, projectDetails41, projectDetails42, projectDetails43, projectDetails44, projectDetails45, projectDetails46, projectDetails47, projectDetails48, projectDetails49, projectDetails50, projectDetails51, projectDetails52, projectDetails53, projectDetails54, projectDetails55, projectDetails56, projectDetails57, projectDetails58, projectDetails59, projectDetails60, projectDetails61, projectDetails62, projectDetails63, projectDetails64, projectDetails65, projectDetails66, projectDetails67, projectDetails68, projectDetails69, projectDetails70, projectDetails71, projectDetails72, projectDetails73, projectDetails74, projectDetails75, projectDetails76, projectDetails77, projectDetails78, projectDetails79, projectDetails80, projectDetails81, projectDetails82, projectDetails83, projectDetails84, projectDetails85, projectDetails86, projectDetails87, projectDetails88, projectDetails89, projectDetails90, projectDetails91, projectDetails92, projectDetails93, projectDetails94 from it_count
    </sql>

    <select id="selectItCountList" parameterType="ItCount" resultMap="ItCountResult">
        <include refid="selectItCountVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''"> and jobNumber like concat('%', #{jobNumber}, '%')</if>
            <if test="jobType1 != null  and jobType1 != ''"> and jobType1 = #{jobType1}</if>
            <if test="jobType2 != null  and jobType2 != ''"> and jobType2 = #{jobType2}</if>
            <if test="jobType3 != null  and jobType3 != ''"> and jobType3 = #{jobType3}</if>
            <if test="jobType4 != null  and jobType4 != ''"> and jobType4 = #{jobType4}</if>
            <if test="jobType5 != null  and jobType5 != ''"> and jobType5 = #{jobType5}</if>
            <if test="jobType6 != null  and jobType6 != ''"> and jobType6 = #{jobType6}</if>
            <if test="projectType1 != null  and projectType1 != ''"> and projectType1 = #{projectType1}</if>
            <if test="projectType2 != null  and projectType2 != ''"> and projectType2 = #{projectType2}</if>
            <if test="projectType3 != null  and projectType3 != ''"> and projectType3 = #{projectType3}</if>
            <if test="projectType4 != null  and projectType4 != ''"> and projectType4 = #{projectType4}</if>
            <if test="projectType5 != null  and projectType5 != ''"> and projectType5 = #{projectType5}</if>
            <if test="projectType6 != null  and projectType6 != ''"> and projectType6 = #{projectType6}</if>
            <if test="projectType7 != null  and projectType7 != ''"> and projectType7 = #{projectType7}</if>
            <if test="projectType8 != null  and projectType8 != ''"> and projectType8 = #{projectType8}</if>
            <if test="projectType9 != null  and projectType9 != ''"> and projectType9 = #{projectType9}</if>
            <if test="projectType10 != null  and projectType10 != ''"> and projectType10 = #{projectType10}</if>
            <if test="projectType11 != null  and projectType11 != ''"> and projectType11 = #{projectType11}</if>
            <if test="projectType12 != null  and projectType12 != ''"> and projectType12 = #{projectType12}</if>
            <if test="projectType13 != null  and projectType13 != ''"> and projectType13 = #{projectType13}</if>
            <if test="projectType14 != null  and projectType14 != ''"> and projectType14 = #{projectType14}</if>
            <if test="projectType15 != null  and projectType15 != ''"> and projectType15 = #{projectType15}</if>
            <if test="projectType16 != null  and projectType16 != ''"> and projectType16 = #{projectType16}</if>
            <if test="projectType17 != null  and projectType17 != ''"> and projectType17 = #{projectType17}</if>
            <if test="projectType18 != null  and projectType18 != ''"> and projectType18 = #{projectType18}</if>
            <if test="projectType19 != null  and projectType19 != ''"> and projectType19 = #{projectType19}</if>
            <if test="projectType20 != null  and projectType20 != ''"> and projectType20 = #{projectType20}</if>
            <if test="projectType21 != null  and projectType21 != ''"> and projectType21 = #{projectType21}</if>
            <if test="projectType22 != null  and projectType22 != ''"> and projectType22 = #{projectType22}</if>
            <if test="projectType23 != null  and projectType23 != ''"> and projectType23 = #{projectType23}</if>
            <if test="projectType24 != null  and projectType24 != ''"> and projectType24 = #{projectType24}</if>
            <if test="projectType25 != null  and projectType25 != ''"> and projectType25 = #{projectType25}</if>
            <if test="projectType26 != null  and projectType26 != ''"> and projectType26 = #{projectType26}</if>
            <if test="projectType27 != null  and projectType27 != ''"> and projectType27 = #{projectType27}</if>
            <if test="projectType28 != null  and projectType28 != ''"> and projectType28 = #{projectType28}</if>
            <if test="projectType29 != null  and projectType29 != ''"> and projectType29 = #{projectType29}</if>
            <if test="projectType30 != null  and projectType30 != ''"> and projectType30 = #{projectType30}</if>
            <if test="projectType31 != null  and projectType31 != ''"> and projectType31 = #{projectType31}</if>
            <if test="projectType32 != null  and projectType32 != ''"> and projectType32 = #{projectType32}</if>
            <if test="projectType33 != null  and projectType33 != ''"> and projectType33 = #{projectType33}</if>
            <if test="projectType34 != null  and projectType34 != ''"> and projectType34 = #{projectType34}</if>
            <if test="projectDetails1 != null  and projectDetails1 != ''"> and projectDetails1 = #{projectDetails1}</if>
            <if test="projectDetails2 != null  and projectDetails2 != ''"> and projectDetails2 = #{projectDetails2}</if>
            <if test="projectDetails3 != null  and projectDetails3 != ''"> and projectDetails3 = #{projectDetails3}</if>
            <if test="projectDetails4 != null  and projectDetails4 != ''"> and projectDetails4 = #{projectDetails4}</if>
            <if test="projectDetails5 != null  and projectDetails5 != ''"> and projectDetails5 = #{projectDetails5}</if>
            <if test="projectDetails6 != null  and projectDetails6 != ''"> and projectDetails6 = #{projectDetails6}</if>
            <if test="projectDetails7 != null  and projectDetails7 != ''"> and projectDetails7 = #{projectDetails7}</if>
            <if test="projectDetails8 != null  and projectDetails8 != ''"> and projectDetails8 = #{projectDetails8}</if>
            <if test="projectDetails9 != null  and projectDetails9 != ''"> and projectDetails9 = #{projectDetails9}</if>
            <if test="projectDetails10 != null  and projectDetails10 != ''"> and projectDetails10 = #{projectDetails10}</if>
            <if test="projectDetails11 != null  and projectDetails11 != ''"> and projectDetails11 = #{projectDetails11}</if>
            <if test="projectDetails12 != null  and projectDetails12 != ''"> and projectDetails12 = #{projectDetails12}</if>
            <if test="projectDetails13 != null  and projectDetails13 != ''"> and projectDetails13 = #{projectDetails13}</if>
            <if test="projectDetails14 != null  and projectDetails14 != ''"> and projectDetails14 = #{projectDetails14}</if>
            <if test="projectDetails15 != null  and projectDetails15 != ''"> and projectDetails15 = #{projectDetails15}</if>
            <if test="projectDetails16 != null  and projectDetails16 != ''"> and projectDetails16 = #{projectDetails16}</if>
            <if test="projectDetails17 != null  and projectDetails17 != ''"> and projectDetails17 = #{projectDetails17}</if>
            <if test="projectDetails18 != null  and projectDetails18 != ''"> and projectDetails18 = #{projectDetails18}</if>
            <if test="projectDetails19 != null  and projectDetails19 != ''"> and projectDetails19 = #{projectDetails19}</if>
            <if test="projectDetails20 != null  and projectDetails20 != ''"> and projectDetails20 = #{projectDetails20}</if>
            <if test="projectDetails21 != null  and projectDetails21 != ''"> and projectDetails21 = #{projectDetails21}</if>
            <if test="projectDetails22 != null  and projectDetails22 != ''"> and projectDetails22 = #{projectDetails22}</if>
            <if test="projectDetails23 != null  and projectDetails23 != ''"> and projectDetails23 = #{projectDetails23}</if>
            <if test="projectDetails24 != null  and projectDetails24 != ''"> and projectDetails24 = #{projectDetails24}</if>
            <if test="projectDetails25 != null  and projectDetails25 != ''"> and projectDetails25 = #{projectDetails25}</if>
            <if test="projectDetails26 != null  and projectDetails26 != ''"> and projectDetails26 = #{projectDetails26}</if>
            <if test="projectDetails27 != null  and projectDetails27 != ''"> and projectDetails27 = #{projectDetails27}</if>
            <if test="projectDetails28 != null  and projectDetails28 != ''"> and projectDetails28 = #{projectDetails28}</if>
            <if test="projectDetails29 != null  and projectDetails29 != ''"> and projectDetails29 = #{projectDetails29}</if>
            <if test="projectDetails30 != null  and projectDetails30 != ''"> and projectDetails30 = #{projectDetails30}</if>
            <if test="projectDetails31 != null  and projectDetails31 != ''"> and projectDetails31 = #{projectDetails31}</if>
            <if test="projectDetails32 != null  and projectDetails32 != ''"> and projectDetails32 = #{projectDetails32}</if>
            <if test="projectDetails33 != null  and projectDetails33 != ''"> and projectDetails33 = #{projectDetails33}</if>
            <if test="projectDetails34 != null  and projectDetails34 != ''"> and projectDetails34 = #{projectDetails34}</if>
            <if test="projectDetails35 != null  and projectDetails35 != ''"> and projectDetails35 = #{projectDetails35}</if>
            <if test="projectDetails36 != null  and projectDetails36 != ''"> and projectDetails36 = #{projectDetails36}</if>
            <if test="projectDetails37 != null  and projectDetails37 != ''"> and projectDetails37 = #{projectDetails37}</if>
            <if test="projectDetails38 != null  and projectDetails38 != ''"> and projectDetails38 = #{projectDetails38}</if>
            <if test="projectDetails39 != null  and projectDetails39 != ''"> and projectDetails39 = #{projectDetails39}</if>
            <if test="projectDetails40 != null  and projectDetails40 != ''"> and projectDetails40 = #{projectDetails40}</if>
            <if test="projectDetails41 != null  and projectDetails41 != ''"> and projectDetails41 = #{projectDetails41}</if>
            <if test="projectDetails42 != null  and projectDetails42 != ''"> and projectDetails42 = #{projectDetails42}</if>
            <if test="projectDetails43 != null  and projectDetails43 != ''"> and projectDetails43 = #{projectDetails43}</if>
            <if test="projectDetails44 != null  and projectDetails44 != ''"> and projectDetails44 = #{projectDetails44}</if>
            <if test="projectDetails45 != null  and projectDetails45 != ''"> and projectDetails45 = #{projectDetails45}</if>
            <if test="projectDetails46 != null  and projectDetails46 != ''"> and projectDetails46 = #{projectDetails46}</if>
            <if test="projectDetails47 != null  and projectDetails47 != ''"> and projectDetails47 = #{projectDetails47}</if>
            <if test="projectDetails48 != null  and projectDetails48 != ''"> and projectDetails48 = #{projectDetails48}</if>
            <if test="projectDetails49 != null  and projectDetails49 != ''"> and projectDetails49 = #{projectDetails49}</if>
            <if test="projectDetails50 != null  and projectDetails50 != ''"> and projectDetails50 = #{projectDetails50}</if>
            <if test="projectDetails51 != null  and projectDetails51 != ''"> and projectDetails51 = #{projectDetails51}</if>
            <if test="projectDetails52 != null  and projectDetails52 != ''"> and projectDetails52 = #{projectDetails52}</if>
            <if test="projectDetails53 != null  and projectDetails53 != ''"> and projectDetails53 = #{projectDetails53}</if>
            <if test="projectDetails54 != null  and projectDetails54 != ''"> and projectDetails54 = #{projectDetails54}</if>
            <if test="projectDetails55 != null  and projectDetails55 != ''"> and projectDetails55 = #{projectDetails55}</if>
            <if test="projectDetails56 != null  and projectDetails56 != ''"> and projectDetails56 = #{projectDetails56}</if>
            <if test="projectDetails57 != null  and projectDetails57 != ''"> and projectDetails57 = #{projectDetails57}</if>
            <if test="projectDetails58 != null  and projectDetails58 != ''"> and projectDetails58 = #{projectDetails58}</if>
            <if test="projectDetails59 != null  and projectDetails59 != ''"> and projectDetails59 = #{projectDetails59}</if>
            <if test="projectDetails60 != null  and projectDetails60 != ''"> and projectDetails60 = #{projectDetails60}</if>
            <if test="projectDetails61 != null  and projectDetails61 != ''"> and projectDetails61 = #{projectDetails61}</if>
            <if test="projectDetails62 != null  and projectDetails62 != ''"> and projectDetails62 = #{projectDetails62}</if>
            <if test="projectDetails63 != null  and projectDetails63 != ''"> and projectDetails63 = #{projectDetails63}</if>
            <if test="projectDetails64 != null  and projectDetails64 != ''"> and projectDetails64 = #{projectDetails64}</if>
            <if test="projectDetails65 != null  and projectDetails65 != ''"> and projectDetails65 = #{projectDetails65}</if>
            <if test="projectDetails66 != null  and projectDetails66 != ''"> and projectDetails66 = #{projectDetails66}</if>
            <if test="projectDetails67 != null  and projectDetails67 != ''"> and projectDetails67 = #{projectDetails67}</if>
            <if test="projectDetails68 != null  and projectDetails68 != ''"> and projectDetails68 = #{projectDetails68}</if>
            <if test="projectDetails69 != null  and projectDetails69 != ''"> and projectDetails69 = #{projectDetails69}</if>
            <if test="projectDetails70 != null  and projectDetails70 != ''"> and projectDetails70 = #{projectDetails70}</if>
            <if test="projectDetails71 != null  and projectDetails71 != ''"> and projectDetails71 = #{projectDetails71}</if>
            <if test="projectDetails72 != null  and projectDetails72 != ''"> and projectDetails72 = #{projectDetails72}</if>
            <if test="projectDetails73 != null  and projectDetails73 != ''"> and projectDetails73 = #{projectDetails73}</if>
            <if test="projectDetails74 != null  and projectDetails74 != ''"> and projectDetails74 = #{projectDetails74}</if>
            <if test="projectDetails75 != null  and projectDetails75 != ''"> and projectDetails75 = #{projectDetails75}</if>
            <if test="projectDetails76 != null  and projectDetails76 != ''"> and projectDetails76 = #{projectDetails76}</if>
            <if test="projectDetails77 != null  and projectDetails77 != ''"> and projectDetails77 = #{projectDetails77}</if>
            <if test="projectDetails78 != null  and projectDetails78 != ''"> and projectDetails78 = #{projectDetails78}</if>
            <if test="projectDetails79 != null  and projectDetails79 != ''"> and projectDetails79 = #{projectDetails79}</if>
            <if test="projectDetails80 != null  and projectDetails80 != ''"> and projectDetails80 = #{projectDetails80}</if>
            <if test="projectDetails81 != null  and projectDetails81 != ''"> and projectDetails81 = #{projectDetails81}</if>
            <if test="projectDetails82 != null  and projectDetails82 != ''"> and projectDetails82 = #{projectDetails82}</if>
            <if test="projectDetails83 != null  and projectDetails83 != ''"> and projectDetails83 = #{projectDetails83}</if>
            <if test="projectDetails84 != null  and projectDetails84 != ''"> and projectDetails84 = #{projectDetails84}</if>
            <if test="projectDetails85 != null  and projectDetails85 != ''"> and projectDetails85 = #{projectDetails85}</if>
            <if test="projectDetails86 != null  and projectDetails86 != ''"> and projectDetails86 = #{projectDetails86}</if>
            <if test="projectDetails87 != null  and projectDetails87 != ''"> and projectDetails87 = #{projectDetails87}</if>
            <if test="projectDetails88 != null  and projectDetails88 != ''"> and projectDetails88 = #{projectDetails88}</if>
            <if test="projectDetails89 != null  and projectDetails89 != ''"> and projectDetails89 = #{projectDetails89}</if>
            <if test="projectDetails90 != null  and projectDetails90 != ''"> and projectDetails90 = #{projectDetails90}</if>
            <if test="projectDetails91 != null  and projectDetails91 != ''"> and projectDetails91 = #{projectDetails91}</if>
            <if test="projectDetails92 != null  and projectDetails92 != ''"> and projectDetails92 = #{projectDetails92}</if>
            <if test="projectDetails93 != null  and projectDetails93 != ''"> and projectDetails93 = #{projectDetails93}</if>
            <if test="projectDetails94 != null  and projectDetails94 != ''"> and projectDetails94 = #{projectDetails94}</if>
        </where>
    </select>
</mapper>