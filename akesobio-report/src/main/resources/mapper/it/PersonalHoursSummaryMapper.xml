<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.it.mapper.PersonalHoursSummaryMapper">

    <resultMap type="PersonalHoursSummary" id="PersonalHoursSummaryResult">
        <result property="name" column="name"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="totalStandardWorkingHours" column="totalStandardWorkingHours"/>
        <result property="totalCompletedWorkHours" column="totalCompletedWorkHours"/>
    </resultMap>

    <select id="selectPersonalHoursSummaryList" parameterType="PersonalHoursSummary"
            resultMap="PersonalHoursSummaryResult">
        select
        *
        from(
        select
        b.fd_name AS 'name',
        a.fd_jobno AS 'jobNumber',
        SUM (c.fd_hour) AS 'totalStandardWorkingHours',
        SUM(CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10, 1))) as 'totalCompletedWorkHours'
        FROM ekp_gzit_workplan a
        LEFT JOIN hr_org_element b ON b.fd_id = a.fd_applicant
        LEFT JOIN ekp_t_workplan_list c ON c.fd_parent_id = a.fd_id
        <where>
            <if test="params.beginDateCreated != null  and params.beginDateCreated != '' and params.endDateCreated != null  and params.endDateCreated != '' ">
                and c.fd_completiontime between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            <if test="name != null  and name != ''">and b.fd_name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''">and a.fd_jobno like concat('%', #{jobNumber}, '%')</if>
            and
            c.fd_state ='2'
            and
            b.fd_name not in ('陈鸿伟','赖俐君','刘文光','王嘉威','萧剑图','谢晓研','陈鸿龙')
        </where>
        GROUP BY
        a.fd_jobno,
        b.fd_name) d
    </select>
</mapper>