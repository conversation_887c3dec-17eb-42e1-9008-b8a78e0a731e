<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.it.mapper.SystemDevelopmentMapper">

    <resultMap type="SystemDevelopment" id="SystemDevelopmentResult">
        <result property="name"    column="name"    />
        <result property="jobNumber"    column="jobNumber"    />
        <result property="oa"    column="oa"    />
        <result property="bi"    column="bi"    />
        <result property="sap"    column="sap"    />
        <result property="shr"    column="shr"    />
        <result property="dms"    column="dms"    />
        <result property="tms"    column="tms"    />
        <result property="qms"    column="qms"    />
        <result property="lims"    column="lims"    />
        <result property="srm"    column="srm"    />
        <result property="wms"    column="wms"    />
        <result property="cam"    column="cam"    />
        <result property="invoiceIdentification"    column="invoiceIdentification"    />
        <result property="electronicSeal"    column="electronicSeal"    />
        <result property="contractSystem"    column="contractSystem"    />
        <result property="reportingSystem"    column="reportingSystem"    />
        <result property="examinationSystem"    column="examinationSystem"    />
        <result property="form"    column="form"    />
        <result property="interfaceDevelopment"    column="interfaceDevelopment"    />
        <result property="function"    column="function"    />
        <result property="reportForms"    column="reportForms"    />
        <result property="test"    column="test"    />
        <result property="database"    column="database"    />
        <result property="requirementAnalysis"    column="requirementAnalysis"    />
        <result property="requirementAnalysisTwo"    column="requirementAnalysisTwo"    />
        <result property="permissionRole"    column="permissionRole"    />
        <result property="developmentDocumentWriting"    column="developmentDocumentWriting"    />
        <result property="unitRequirementsAnalysis"    column="unitRequirementsAnalysis"    />
        <result property="unitTesting"    column="unitTesting"    />
        <result property="integrationTesting"    column="integrationTesting"    />
    </resultMap>
    <select id="selectSystemDevelopmentList" parameterType="SystemDevelopment" resultMap="SystemDevelopmentResult">
        select * from(
        SELECT
        b.fd_name as 'name',
        a.fd_jobno as 'jobNumber',
        SUM(CASE c.fd_worktype2_text WHEN 'OA' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'oa',
        SUM(CASE c.fd_worktype2_text WHEN 'BI' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'bi',
        SUM(CASE c.fd_worktype2_text WHEN 'SAP' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'sap',
        SUM(CASE c.fd_worktype2_text WHEN 'SHR' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'shr',
        SUM(CASE c.fd_worktype2_text WHEN 'DMS' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'dms',
        SUM(CASE c.fd_worktype2_text WHEN 'TMS' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'tms',
        SUM(CASE c.fd_worktype2_text WHEN 'QMS' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'qms',
        SUM(CASE c.fd_worktype2_text WHEN 'LIMS' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'lims',
        SUM(CASE c.fd_worktype2_text WHEN 'SRM' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'srm',
        SUM(CASE c.fd_worktype2_text WHEN 'WMS' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'wms',
        SUM(CASE c.fd_worktype2_text WHEN 'CRM' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'crm',
        SUM(CASE c.fd_worktype2_text WHEN '发票识别' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'invoiceIdentification',
        SUM(CASE c.fd_worktype2_text WHEN '电子印章' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'electronicSeal',
        SUM(CASE c.fd_worktype2_text WHEN '合同系统' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'contractSystem',
        SUM(CASE c.fd_worktype2_text WHEN '报表系统' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'reportingSystem',
        SUM(CASE c.fd_worktype2_text WHEN '考试系统' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'examinationSystem',
        SUM(CASE c.fd_worktype4_text WHEN '表单' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'form',
        SUM(CASE c.fd_worktype4_text WHEN '接口' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'interfaceDevelopment',
        SUM(CASE c.fd_worktype4_text WHEN '功能' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'function',
        SUM(CASE c.fd_worktype4_text WHEN '报表' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'reportForms',
        SUM(CASE c.fd_worktype4_text WHEN '数据库' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'database',
        SUM(CASE c.fd_worktype4_text WHEN '测试' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'test',
        SUM(CASE c.fd_worktype4_text WHEN '需求分析' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'requirementAnalysis',
        SUM(CASE c.fd_worktype4_text WHEN '需求分析（二开）' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'requirementAnalysisTwo',
        SUM(CASE c.fd_worktype4_text WHEN '权限角色' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'permissionRole',
        SUM(CASE c.fd_worktype4_text WHEN '开发文档编写' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'developmentDocumentWriting',
        SUM(CASE c.fd_worktype4_text WHEN '单元需求分析' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'unitRequirementsAnalysis',
        SUM(CASE c.fd_worktype4_text WHEN '单元测试' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'unitTesting',
        SUM(CASE c.fd_worktype4_text WHEN '集成测试' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'integrationTesting'
        from  	ekp_gzit_workplan a
        left join hr_org_element b on b.fd_id=a.fd_applicant
        left join ekp_t_workplan_list c on  c.fd_parent_id=a.fd_id
        <where>
            <if test="name != null  and name != ''"> and b.fd_name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''"> and a.fd_jobno like concat('%', #{jobNumber}, '%')</if>
            <if test="params.beginDateCreated != null  and params.beginDateCreated != '' and params.endDateCreated != null  and params.endDateCreated != '' ">
                and c.fd_completiontime between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            and
            c.fd_worktype1_text='系统开发'
            and
            c.fd_state ='2'
            and
            b.fd_name not in ('陈鸿伟','赖俐君','刘文光','王嘉威','萧剑图','谢晓研','陈鸿龙')
        </where>
        GROUP BY
        a.fd_jobno,b.fd_name
        )d
    </select>
</mapper>