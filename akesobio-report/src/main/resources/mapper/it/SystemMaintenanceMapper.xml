<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.it.mapper.SystemMaintenanceMapper">

    <resultMap type="SystemMaintenance" id="SystemMaintenanceResult">
        <result property="name" column="name"/>
        <result property="jobNumber" column="jobNumber"/>
        <result property="oa" column="oa"/>
        <result property="sap" column="sap"/>
        <result property="shr" column="shr"/>
        <result property="invoiceIdentification" column="invoiceIdentification"/>
        <result property="electronicSeal" column="electronicSeal"/>
        <result property="mailboxSystem" column="mailboxSystem"/>
        <result property="rfFees" column="rfFees"/>
        <result property="examinationSystem" column="examinationSystem"/>
        <result property="form" column="form"/>
        <result property="interfaceDevelopment" column="interfaceDevelopment"/>
        <result property="function" column="function"/>
        <result property="reportForms" column="reportForms"/>
        <result property="database" column="database"/>
        <result property="test" column="test"/>
        <result property="problem" column="problem"/>
        <result property="exceptionInvestigation" column="exceptionInvestigation"/>
        <result property="processFormException" column="processFormException"/>
        <result property="interfaceException" column="interfaceException"/>
        <result property="systemUpdates" column="systemUpdates"/>
        <result property="permissionRole" column="permissionRole"/>
        <result property="systemConfigurationChanges" column="systemConfigurationChanges"/>
        <result property="userOperationGuidelines" column="userOperationGuidelines"/>
        <result property="userTraining" column="userTraining"/>
        <result property="userOperationDocumentWriting" column="userOperationDocumentWriting"/>
        <result property="simplifiedProgramModification" column="simplifiedProgramModification"/>
        <result property="dataCompilation" column="dataCompilation"/>
        <result property="email" column="email"/>
        <result property="issueTracking" column="issueTracking"/>
        <result property="assistanceCooperation" column="assistanceCooperation"/>
    </resultMap>

    <select id="selectSystemMaintenanceList" parameterType="SystemMaintenance" resultMap="SystemMaintenanceResult">
        select * from (
        SELECT
        b.fd_name as 'name',
        a.fd_jobno as 'jobNumber',
        SUM(CASE c.fd_worktype2_text WHEN 'OA' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'oa',
        SUM(CASE c.fd_worktype2_text WHEN 'SAP' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'sap',
        SUM(CASE c.fd_worktype2_text WHEN 'SHR' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'shr',
        SUM(CASE c.fd_worktype2_text WHEN '发票识别' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'invoiceIdentification',
        SUM(CASE c.fd_worktype2_text WHEN '电子印章' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'electronicSeal',
        SUM(CASE c.fd_worktype2_text WHEN '邮箱系统' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'mailboxSystem',
        SUM(CASE c.fd_worktype2_text WHEN 'RF费用' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'rfFees',
        SUM(CASE c.fd_worktype2_text WHEN '考试系统' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'examinationSystem',
        SUM(CASE c.fd_worktype4_text WHEN '表单' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'form',
        SUM(CASE c.fd_worktype4_text WHEN '接口' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'interfaceDevelopment',
        SUM(CASE c.fd_worktype4_text WHEN '功能' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'function',
        SUM(CASE c.fd_worktype4_text WHEN '报表' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'reportForms',
        SUM(CASE c.fd_worktype4_text WHEN '数据库' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'database',
        SUM(CASE c.fd_worktype4_text WHEN '测试' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'test',
        SUM(CASE c.fd_worktype4_text WHEN '问题答疑' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'problem',
        SUM(CASE c.fd_worktype4_text WHEN '异常排查处理' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'exceptionInvestigation',
        SUM(CASE c.fd_worktype4_text WHEN '流程表单异常' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'processFormException',
        SUM(CASE c.fd_worktype4_text WHEN '接口异常' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'interfaceException',
        SUM(CASE c.fd_worktype4_text WHEN '系统更新' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'systemUpdates',
        SUM(CASE c.fd_worktype4_text WHEN '权限角色' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'permissionRole',
        SUM(CASE c.fd_worktype4_text WHEN '系统配置更改' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'systemConfigurationChanges',
        SUM(CASE c.fd_worktype4_text WHEN '用户操作指引' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'userOperationGuidelines',
        SUM(CASE c.fd_worktype4_text WHEN '用户培训' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'userTraining',
        SUM(CASE c.fd_worktype4_text WHEN '用户操作文档编写' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'userOperationDocumentWriting',
        SUM(CASE c.fd_worktype4_text WHEN '简易程序修改' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'simplifiedProgramModification',
        SUM(CASE c.fd_worktype4_text WHEN '资料整理' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'dataCompilation',
        SUM(CASE c.fd_worktype4_text WHEN '邮箱运维' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as 'email',
        SUM(CASE c.fd_worktype4_text WHEN '问题单跟进' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'issueTracking',
        SUM(CASE c.fd_worktype4_text WHEN '协助配合' THEN CAST(c.fd_3bbff8e804bd98 AS DECIMAL(10,1)) else 0 END) as
        'assistanceCooperation'
        from ekp_gzit_workplan a
        left join hr_org_element b on b.fd_id=a.fd_applicant
        left join ekp_t_workplan_list c on c.fd_parent_id=a.fd_id
        <where>
            <if test="name != null  and name != ''">and b.fd_name like concat('%', #{name}, '%')</if>
            <if test="jobNumber != null  and jobNumber != ''">and a.fd_jobno like concat('%', #{jobNumber}, '%')</if>
            <if test="params.beginDateCreated != null  and params.beginDateCreated != '' and params.endDateCreated != null  and params.endDateCreated != '' ">
                and c.fd_completiontime between #{params.beginDateCreated} and #{params.endDateCreated}
            </if>
            and
            c.fd_worktype1_text='系统运维'
            and
            c.fd_state ='2'
            and
            b.fd_name not in ('陈鸿伟','赖俐君','刘文光','王嘉威','萧剑图','谢晓研','陈鸿龙')
        </where>
        GROUP BY
        a.fd_jobno,b.fd_name
        )d
    </select>

</mapper>