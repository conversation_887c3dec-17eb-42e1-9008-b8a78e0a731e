<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.akesobio.report.pv.mapper.Pv1Mapper">

    <resultMap type="com.akesobio.report.pv.domain.Pv1" id="Pv1Result">
        <result property="caseId" column="CASE_ID"/>
        <result property="caseNum" column="CASE_NUM"/>
        <result property="evtDescReptdJ" column="EVT_DESC_REPTD_J"/>
        <result property="evtPtCode" column="EVT_PT_CODE"/>
        <result property="evtPrefTermJ" column="EVT_PREF_TERM_J"/>
        <result property="evtPrefTerm" column="EVT_PREF_TERM"/>
        <result property="evtOnset" column="EVT_ONSET"/>
        <result property="evtStopDate" column="EVT_STOP_DATE"/>
        <result property="evtEvtOutcomeJ" column="EVT_EVT_OUTCOME_J"/>
        <result property="evtSeriousnessJ" column="EVT_SERIOUSNESS_J"/>
        <result property="evtEvtIntensityJ" column="EVT_EVT_INTENSITY_J"/>
        <result property="evtCtcaeJ" column="EVT_CTCAE_J"/>
        <result property="evtDetails" column="EVT_DETAILS"/>
        <result property="stuStudyNum" column="STU_STUDY_NUM"/>
        <result property="masInitReptDate" column="MAS_INIT_REPT_DATE"/>
        <result property="fupReceiptDate" column="FUP_RECEIPT_DATE"/>
        <result property="masStateName" column="MAS_STATE_NAME"/>
        <result property="patPatSubjNum" column="PAT_PAT_SUBJ_NUM"/>
        <result property="patGenderId" column="PAT_GENDER_ID"/>
        <result property="patPatDob" column="PAT_PAT_DOB"/>
        <result property="proGenericNameJ" column="PRO_GENERIC_NAME_J"/>
        <result property="finalDesc" column="FINAL_DESC"/>
        <result property="evnEvtNatureJ" column="EVN_EVT_NATURE_J"/>
        <result property="evlDetListednessId" column="EVL_DET_LISTEDNESS_ID"/>
        <result property="evdActionTakenJ" column="EVD_ACTION_TAKEN_J"/>
        <result property="evcDetCausalityJ" column="EVC_DET_CAUSALITY_J"/>
        <result property="evcRptCausalityJ" column="EVC_RPT_CAUSALITY_J"/>
        <result property="anaNarrativeJ" column="ANA_NARRATIVE_J"/>
        <result property="anaCaseCompanyCmtsJ" column="ANA_CASE_COMPANY_CMTS_J"/>
        <result property="namej" column="NAME_J"/>
        <result property="familyId" column="FAMILY_ID"/>
        <result property="protocolDesc" column="protocol_desc"/>
        <result property="protocolId" column="protocol_id"/>
        <result property="studyNum" column="study_num"/>
        <result property="studyKey" column="study_key"/>
        <result property="blindNameJ" column="blind_name_j"/>
        <result property="cohortId" column="cohort_id"/>
    </resultMap>

    <sql id="selectPv1Vo">
        select CASE_ID,
               CASE_NUM,
               EVT_DESC_REPTD_J,
               EVT_PT_CODE,
               EVT_PREF_TERM_J,
               EVT_PREF_TERM,
               EVT_ONSET,
               EVT_STOP_DATE,
               EVT_EVT_OUTCOME_J,
               EVT_SERIOUSNESS_J,
               EVT_EVT_INTENSITY_J,
               EVT_CTCAE_J,
               EVT_DETAILS,
               STU_STUDY_NUM,
               MAS_INIT_REPT_DATE,
               FUP_RECEIPT_DATE,
               MAS_STATE_NAME,
               PAT_PAT_SUBJ_NUM,
               PAT_GENDER_ID,
               PAT_PAT_DOB,
               PRO_GENERIC_NAME,
               FINAL_DESC,
               EVN_EVT_NATURE_J,
               EVL_DET_LISTEDNESS_ID,
               EVD_ACTION_TAKEN_J,
               EVC_DET_CAUSALITY_J,
               EVC_RPT_CAUSALITY_J,
               ANA_NARRATIVE_J,
               ANA_CASE_COMPANY_CMTS_J
        from PV_1
    </sql>

    <select id="selectPv1List" parameterType="com.akesobio.report.pv.domain.Pv1" resultMap="Pv1Result">
        SELECT
        A.CASE_ID,
        A.CASE_NUM,
        A.EVT_DESC_REPTD_J,
        A.EVT_PT_CODE,
        A.EVT_PREF_TERM_J,
        A.EVT_PREF_TERM,
        TO_CHAR(A.EVT_ONSET, 'YYYY-MM-DD') AS "EVT_ONSET",
        TO_CHAR(A.EVT_STOP_DATE, 'YYYY-MM-DD') AS "EVT_STOP_DATE",
        A.EVT_EVT_OUTCOME_J,
        A.EVT_SERIOUSNESS_J,
        A.EVT_EVT_INTENSITY_J,
        A.EVT_CTCAE_J,
        A.EVT_DETAILS_J,
        B.STU_STUDY_NUM,
        TO_CHAR(C.MAS_INIT_REPT_DATE, 'YYYY-MM-DD') AS "MAS_INIT_REPT_DATE",
        TO_CHAR((CASE
        WHEN D.Min_FUP_RECEIPT_DATE IS NULL THEN
        MAS_INIT_REPT_DATE ELSE D.Min_FUP_RECEIPT_DATE
        END ),'YYYY-MM-DD' ) AS "FUP_RECEIPT_DATE",
        C.MAS_STATE_NAME,
        E.PAT_PAT_SUBJ_NUM,
        (
        CASE E.PAT_GENDER_ID
        WHEN 1 THEN '男'
        WHEN 2 THEN '女'
        ELSE '' -- 如果有其他值，这里可以处理，或者直接移除ELSE部分
        END
        ) AS "PAT_GENDER_ID",
        TO_CHAR(E.PAT_PAT_DOB, 'YYYY-MM-DD') AS "PAT_PAT_DOB",
        F.PRO_GENERIC_NAME_J,
        G.FINAL_DESC,
        H.EVN_EVT_NATURE_J,
        DECODE(I.EVL_DET_LISTEDNESS_ID, 1, '已知', 2, '未知', 3, '不详', '') AS "EVL_DET_LISTEDNESS_ID",
        J.EVD_ACTION_TAKEN_J,
        O.EVC_DET_CAUSALITY_J,
        O.EVC_RPT_CAUSALITY_J,
        M.ANA_NARRATIVE_J,
        M.ANA_CASE_COMPANY_CMTS_J
        FROM
        V_AK_CASE_MASTER C
        JOIN V_AK_CASE_EVENT A ON A.CASE_ID = C.CASE_ID
        LEFT JOIN V_AK_CASE_STUDY B ON B.CASE_ID = C.CASE_ID
        LEFT JOIN (
        SELECT CASE_ID, MAX(FUP_RECEIPT_DATE) AS Min_FUP_RECEIPT_DATE
        FROM V_AK_CASE_FOLLOWUP
        GROUP BY CASE_ID
        ) D ON D.CASE_ID = C.CASE_ID
        JOIN V_AK_CASE_PAT_INFO E ON E.CASE_ID = C.CASE_ID
        JOIN V_AK_CASE_PRODUCT_S F ON F.CASE_ID = C.CASE_ID
        LEFT JOIN V_AK_CASE_EVENTT_CRITERIA G ON G.CASE_ID = C.CASE_ID AND G.SEQ_NUM = A.EVENT_SEQ_NUM
        LEFT JOIN (
        SELECT t1.case_id, t1.evn_event_seq_num, LISTAGG(EVN_EVT_NATURE_J, '，')
        WITHIN GROUP (ORDER BY T1.CASE_ID, t1.evn_event_seq_num, t1.evn_seq_num) EVN_EVT_NATURE_J
        FROM V_AK_CASE_EVENT_NATURE T1
        GROUP BY t1.CASE_ID, t1.evn_event_seq_num
        ) H ON H.CASE_ID = C.CASE_ID AND H.evn_event_seq_num = A.EVENT_SEQ_NUM
        LEFT JOIN V_AK_CASE_EVENT_ASSESS_L I ON I.CASE_ID = C.CASE_ID AND I.EVENT_SEQ_NUM = A.EVENT_SEQ_NUM AND
        I.PROD_SEQ_NUM = F.PROD_SEQ_NUM
        LEFT JOIN V_AK_CASE_EVENT_DETAIL J ON J.CASE_ID = C.CASE_ID AND J.EVENT_SEQ_NUM = A.EVENT_SEQ_NUM AND
        J.PROD_SEQ_NUM = F.PROD_SEQ_NUM
        LEFT JOIN V_AK_CASE_EVENT_ASSESS_C O ON O.CASE_ID = C.CASE_ID AND O.EVENT_SEQ_NUM = A.EVENT_SEQ_NUM AND
        O.PROD_SEQ_NUM = F.PROD_SEQ_NUM
        JOIN V_AK_CASE_ANALYSIS M ON M.CASE_ID = C.CASE_ID
        <where>

            <if test="EVT_ONSET != null ">
                AND
                (
                TO_CHAR(A.EVT_ONSET, 'YYYY-MM-DD') >= #{EVT_ONSET}
                AND TO_CHAR(A.EVT_ONSET, 'YYYY-MM-DD') <![CDATA[ <= ]]> #{EVT_STOP_DATE}
                <if test="events != null and events == 'true'">
                    or EVT_ONSET is  null
                </if>
                <if test="events != null and events == 'false'">
                    and EVT_ONSET is not null
                </if>
                )
            </if>

            <if test="PORT_RECEIPT_START_DATE != null and PORT_RECEIPT_STOP_DATE != null">
               and  (CASE
                WHEN D.Min_FUP_RECEIPT_DATE IS NULL THEN
                MAS_INIT_REPT_DATE
                ELSE
                D.Min_FUP_RECEIPT_DATE
                END )   >= TO_DATE(#{PORT_RECEIPT_START_DATE}, 'yyyy-MM-dd') and  (CASE
                WHEN D.Min_FUP_RECEIPT_DATE IS NULL THEN
                MAS_INIT_REPT_DATE
                ELSE
                D.Min_FUP_RECEIPT_DATE
                END )   <![CDATA[ <= ]]> TO_DATE(#{PORT_RECEIPT_STOP_DATE}, 'yyyy-MM-dd')
            </if>

            <if test="PRO_FAMILY_ID != null">
                AND F.PRO_FAMILY_ID in
                <foreach item="num" collection="PRO_FAMILY_ID" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="STU_PROTOCOL_ID  != null">
                AND B.STU_PROTOCOL_ID in
                <foreach item="num" collection="STU_PROTOCOL_ID" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="STU_STUDY_KEY != null">
                AND B.STU_STUDY_KEY in
                <foreach item="num" collection="STU_STUDY_KEY" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="STU_COHORT_ID != null">
                AND B.STU_COHORT_ID in
                <foreach item="num" collection="STU_COHORT_ID" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>

            <if test="scraps != null and scraps != ''">
                and C.CASE_NUM  not in  (Select CASE_NUM from V_AK_CASE_CLASSIFICATIONS WHERE CLA_CALASSIFICATION_J in
                <foreach item="num" collection="scraps" open="(" separator="," close=")">
                    #{num}
                </foreach>
                )
            </if>
        </where>

    </select>

    <select id="familyId" resultMap="Pv1Result">
        select NAME_J, FAMILY_ID
        from lm_product_family
    </select>
    <select id="protocolId" resultMap="Pv1Result">
        select protocol_desc, protocol_id
        from lm_protocols
        order by protocol_desc
    </select>
    <select id="studyId" resultMap="Pv1Result">
        select study_num, study_key
        from lm_studies
    </select>
    <select id="armId" resultMap="Pv1Result">
        select blind_name_j, cohort_id
        from lm_study_cohorts
    </select>

    <insert id="insertPv1" parameterType="Pv1">
        insert into PV_1
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">CASE_ID,</if>
            <if test="caseNum != null">CASE_NUM,</if>
            <if test="evtDescReptdJ != null">EVT_DESC_REPTD_J,</if>
            <if test="evtPtCode != null">EVT_PT_CODE,</if>
            <if test="evtPrefTermJ != null">EVT_PREF_TERM_J,</if>
            <if test="evtPrefTerm != null">EVT_PREF_TERM,</if>
            <if test="evtOnset != null">EVT_ONSET,</if>
            <if test="evtStopDate != null">EVT_STOP_DATE,</if>
            <if test="evtEvtOutcomeJ != null">EVT_EVT_OUTCOME_J,</if>
            <if test="evtSeriousnessJ != null">EVT_SERIOUSNESS_J,</if>
            <if test="evtEvtIntensityJ != null">EVT_EVT_INTENSITY_J,</if>
            <if test="evtCtcaeJ != null">EVT_CTCAE_J,</if>
            <if test="evtDetails != null">EVT_DETAILS,</if>
            <if test="stuStudyNum != null">STU_STUDY_NUM,</if>
            <if test="masInitReptDate != null">MAS_INIT_REPT_DATE,</if>
            <if test="fupReceiptDate != null">FUP_RECEIPT_DATE,</if>
            <if test="masStateName != null">MAS_STATE_NAME,</if>
            <if test="patPatSubjNum != null">PAT_PAT_SUBJ_NUM,</if>
            <if test="patGenderId != null">PAT_GENDER_ID,</if>
            <if test="patPatDob != null">PAT_PAT_DOB,</if>
            <if test="proGenericName != null">PRO_GENERIC_NAME_J,</if>
            <if test="finalDesc != null">FINAL_DESC,</if>
            <if test="evnEvtNatureJ != null">EVN_EVT_NATURE_J,</if>
            <if test="evlDetListednessId != null">EVL_DET_LISTEDNESS_ID,</if>
            <if test="evdActionTakenJ != null">EVD_ACTION_TAKEN_J,</if>
            <if test="evcDetCausalityJ != null">EVC_DET_CAUSALITY_J,</if>
            <if test="evcRptCausalityJ != null">EVC_RPT_CAUSALITY_J,</if>
            <if test="anaNarrativeJ != null">ANA_NARRATIVE_J,</if>
            <if test="anaCaseCompanyCmtsJ != null">ANA_CASE_COMPANY_CMTS_J,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseId != null">#{caseId},</if>
            <if test="caseNum != null">#{caseNum},</if>
            <if test="evtDescReptdJ != null">#{evtDescReptdJ},</if>
            <if test="evtPtCode != null">#{evtPtCode},</if>
            <if test="evtPrefTermJ != null">#{evtPrefTermJ},</if>
            <if test="evtPrefTerm != null">#{evtPrefTerm},</if>
            <if test="evtOnset != null">#{evtOnset},</if>
            <if test="evtStopDate != null">#{evtStopDate},</if>
            <if test="evtEvtOutcomeJ != null">#{evtEvtOutcomeJ},</if>
            <if test="evtSeriousnessJ != null">#{evtSeriousnessJ},</if>
            <if test="evtEvtIntensityJ != null">#{evtEvtIntensityJ},</if>
            <if test="evtCtcaeJ != null">#{evtCtcaeJ},</if>
            <if test="evtDetails != null">#{evtDetails},</if>
            <if test="stuStudyNum != null">#{stuStudyNum},</if>
            <if test="masInitReptDate != null">#{masInitReptDate},</if>
            <if test="fupReceiptDate != null">#{fupReceiptDate},</if>
            <if test="masStateName != null">#{masStateName},</if>
            <if test="patPatSubjNum != null">#{patPatSubjNum},</if>
            <if test="patGenderId != null">#{patGenderId},</if>
            <if test="patPatDob != null">#{patPatDob},</if>
            <if test="proGenericName != null">#{proGenericName},</if>
            <if test="finalDesc != null">#{finalDesc},</if>
            <if test="evnEvtNatureJ != null">#{evnEvtNatureJ},</if>
            <if test="evlDetListednessId != null">#{evlDetListednessId},</if>
            <if test="evdActionTakenJ != null">#{evdActionTakenJ},</if>
            <if test="evcDetCausalityJ != null">#{evcDetCausalityJ},</if>
            <if test="evcRptCausalityJ != null">#{evcRptCausalityJ},</if>
            <if test="anaNarrativeJ != null">#{anaNarrativeJ},</if>
            <if test="anaCaseCompanyCmtsJ != null">#{anaCaseCompanyCmtsJ},</if>
        </trim>
    </insert>

    <update id="updatePv1" parameterType="Pv1">
        update PV_1
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseNum != null">CASE_NUM = #{caseNum},</if>
            <if test="evtDescReptdJ != null">EVT_DESC_REPTD_J = #{evtDescReptdJ},</if>
            <if test="evtPtCode != null">EVT_PT_CODE = #{evtPtCode},</if>
            <if test="evtPrefTermJ != null">EVT_PREF_TERM_J = #{evtPrefTermJ},</if>
            <if test="evtPrefTerm != null">EVT_PREF_TERM = #{evtPrefTerm},</if>
            <if test="evtOnset != null">EVT_ONSET = #{evtOnset},</if>
            <if test="evtStopDate != null">EVT_STOP_DATE = #{evtStopDate},</if>
            <if test="evtEvtOutcomeJ != null">EVT_EVT_OUTCOME_J = #{evtEvtOutcomeJ},</if>
            <if test="evtSeriousnessJ != null">EVT_SERIOUSNESS_J = #{evtSeriousnessJ},</if>
            <if test="evtEvtIntensityJ != null">EVT_EVT_INTENSITY_J = #{evtEvtIntensityJ},</if>
            <if test="evtCtcaeJ != null">EVT_CTCAE_J = #{evtCtcaeJ},</if>
            <if test="evtDetails != null">EVT_DETAILS = #{evtDetails},</if>
            <if test="stuStudyNum != null">STU_STUDY_NUM = #{stuStudyNum},</if>
            <if test="masInitReptDate != null">MAS_INIT_REPT_DATE = #{masInitReptDate},</if>
            <if test="fupReceiptDate != null">FUP_RECEIPT_DATE = #{fupReceiptDate},</if>
            <if test="masStateName != null">MAS_STATE_NAME = #{masStateName},</if>
            <if test="patPatSubjNum != null">PAT_PAT_SUBJ_NUM = #{patPatSubjNum},</if>
            <if test="patGenderId != null">PAT_GENDER_ID = #{patGenderId},</if>
            <if test="patPatDob != null">PAT_PAT_DOB = #{patPatDob},</if>
            <if test="proGenericName != null">PRO_GENERIC_NAME = #{proGenericName},</if>
            <if test="finalDesc != null">FINAL_DESC = #{finalDesc},</if>
            <if test="evnEvtNatureJ != null">EVN_EVT_NATURE_J = #{evnEvtNatureJ},</if>
            <if test="evlDetListednessId != null">EVL_DET_LISTEDNESS_ID = #{evlDetListednessId},</if>
            <if test="evdActionTakenJ != null">EVD_ACTION_TAKEN_J = #{evdActionTakenJ},</if>
            <if test="evcDetCausalityJ != null">EVC_DET_CAUSALITY_J = #{evcDetCausalityJ},</if>
            <if test="evcRptCausalityJ != null">EVC_RPT_CAUSALITY_J = #{evcRptCausalityJ},</if>
            <if test="anaNarrativeJ != null">ANA_NARRATIVE_J = #{anaNarrativeJ},</if>
            <if test="anaCaseCompanyCmtsJ != null">ANA_CASE_COMPANY_CMTS_J = #{anaCaseCompanyCmtsJ},</if>
        </trim>
        where CASE_ID = #{caseId}
    </update>

    <delete id="deletePv1ByCaseId" parameterType="String">
        delete
        from PV_1
        where CASE_ID = #{caseId}
    </delete>

    <delete id="deletePv1ByCaseIds" parameterType="String">
        delete from PV_1 where CASE_ID in
        <foreach item="caseId" collection="array" open="(" separator="," close=")">
            #{caseId}
        </foreach>
    </delete>
</mapper>



