/**
 * IIT赠药报表前端配置
 * 
 * <AUTHOR>
 * @since 2025/8/1
 */

// 单据状态配置
const DOCUMENT_STATUS_CONFIG = {
    // 状态选项
    options: [
        { code: '', name: '全部状态' },
        { code: 'DRAFT', name: '草稿' },
        { code: 'SUBMIT', name: '已提交' },
        { code: 'APPROVE', name: '已审批' },
        { code: 'REJECT', name: '已驳回' },
        { code: 'CANCEL', name: '已取消' },
        { code: 'CLOSE', name: '已关闭' }
    ],
    
    // 状态颜色映射（用于前端显示）
    colors: {
        'DRAFT': '#909399',      // 灰色 - 草稿
        'SUBMIT': '#409EFF',     // 蓝色 - 已提交
        'APPROVE': '#67C23A',    // 绿色 - 已审批
        'REJECT': '#F56C6C',     // 红色 - 已驳回
        'CANCEL': '#E6A23C',     // 橙色 - 已取消
        'CLOSE': '#909399'       // 灰色 - 已关闭
    },
    
    // 获取状态显示名称
    getStatusName: function(code) {
        const option = this.options.find(opt => opt.code === code);
        return option ? option.name : code;
    },
    
    // 获取状态颜色
    getStatusColor: function(code) {
        return this.colors[code] || '#909399';
    }
};

// 查询表单配置
const QUERY_FORM_CONFIG = {
    // 表单字段配置
    fields: {
        projectNumber: {
            label: '项目号',
            type: 'input',
            placeholder: '请输入项目号'
        },
        iitClass: {
            label: 'IIT分类',
            type: 'input',
            placeholder: '请输入IIT分类'
        },
        setupDepartment: {
            label: '立项部门',
            type: 'input',
            placeholder: '请输入立项部门'
        },
        chargeDepartment: {
            label: '费用承担部门',
            type: 'input',
            placeholder: '请输入费用承担部门'
        },
        iitCenter: {
            label: 'IIT中心',
            type: 'input',
            placeholder: '请输入IIT中心'
        },
        statusList: {
            label: '单据状态',
            type: 'select',
            multiple: true,
            placeholder: '请选择单据状态',
            options: DOCUMENT_STATUS_CONFIG.options.filter(opt => opt.code !== '') // 排除"全部状态"选项
        }
    }
};

// 表格列配置
const TABLE_COLUMNS_CONFIG = [
    { prop: 'setupDepartmentFullPath', label: '立项部门完整路径', width: 300 },
    { prop: 'iitClass', label: 'IIT分类', width: 120 },
    { prop: 'projectNumber', label: '项目号', width: 150 },
    { prop: 'receiveSysNumber', label: '领药系统单号', width: 180 },
    { prop: 'submitDate', label: '提交日期', width: 120 },
    { prop: 'submitter', label: '领药申请人', width: 120 },
    {
        prop: 'statusName',
        label: '单据状态',
        width: 100,
        // 自定义渲染函数（用于显示带颜色的状态）
        render: function(row) {
            const color = DOCUMENT_STATUS_CONFIG.getStatusColor(row.status);
            return `<span style="color: ${color}; font-weight: bold;">${row.statusName}</span>`;
        }
    },
    { prop: 'chargeDepartmentFullPath', label: '费用承担部门完整路径', width: 300 },
    { prop: 'injectionName', label: '注射剂名称', width: 150 },
    { prop: 'totalNum', label: '总申请数量', width: 120 },
    { prop: 'injectionNum', label: '赠药数量', width: 120 },
    { prop: 'iitCenter', label: 'IIT中心', width: 150 },
    { prop: 'researchDepartment', label: '科研科室', width: 150 },
    { prop: 'receiver', label: '收货人', width: 120 },
    { prop: 'receiverPhone', label: '收货人联系电话', width: 150 },
    { prop: 'receiveAddress', label: '收货地址', width: 200 },
    { prop: 'signDepartment', label: '签收部门', width: 150 }
];

// 导出配置
const EXPORT_CONFIG = {
    fileName: 'IIT赠药报表',
    sheetName: 'IIT赠药数据'
};

// 工具函数
const IITDonationReportUtils = {
    /**
     * 格式化查询参数
     */
    formatQueryParams: function(params) {
        // 处理状态参数
        if (params.statusList && params.statusList.length === 0) {
            delete params.statusList;
        }
        
        // 移除空字符串参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });
        
        return params;
    },
    
    /**
     * 验证查询参数
     */
    validateQueryParams: function(params) {
        // 验证状态参数
        if (params.statusList && params.statusList.length > 0) {
            const validCodes = DOCUMENT_STATUS_CONFIG.options.map(opt => opt.code).filter(code => code !== '');
            const invalidCodes = params.statusList.filter(code => !validCodes.includes(code));
            
            if (invalidCodes.length > 0) {
                throw new Error('无效的状态代码: ' + invalidCodes.join(', '));
            }
        }
        
        return true;
    }
};

// 导出配置对象
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DOCUMENT_STATUS_CONFIG,
        QUERY_FORM_CONFIG,
        TABLE_COLUMNS_CONFIG,
        EXPORT_CONFIG,
        IITDonationReportUtils
    };
}
