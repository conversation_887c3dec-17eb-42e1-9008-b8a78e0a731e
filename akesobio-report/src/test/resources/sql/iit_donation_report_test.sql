-- IIT赠药报表功能测试SQL
-- 用于验证单据状态功能是否正常工作

-- 1. 测试基本查询（包含状态字段）
SELECT 
    h_drug_req.document_num AS '领药系统单号',
    h_drug_req.status AS '原始状态代码',
    CASE h_drug_req.status
        WHEN 'DRAFT' THEN '草稿'
        WHEN 'SUBMIT' THEN '已提交'
        WHEN 'APPROVE' THEN '已审批'
        WHEN 'REJECT' THEN '已驳回'
        WHEN 'CANCEL' THEN '已取消'
        WHEN 'CLOSE' THEN '已关闭'
        ELSE h_drug_req.status
    END AS '状态中文名称',
    u.full_name AS '领药申请人',
    DATE_FORMAT(h_iit_req.submit_date, '%Y-%m-%d') AS '提交日期'
FROM exp_claim_header AS h_drug_req
JOIN exp_header_type AS ht_drug ON h_drug_req.header_type_id = ht_drug.type_id AND ht_drug.type_code = 'YXIIT02'
JOIN fnd_user AS u ON h_drug_req.submit_user = u.user_id
LEFT JOIN exp_claim_header AS h_iit_req ON h_drug_req.link_header_id = h_iit_req.header_id
WHERE h_drug_req.status != 'deleted'
LIMIT 10;

-- 2. 测试状态过滤（单个状态）
SELECT COUNT(*) AS '草稿状态单据数量'
FROM exp_claim_header AS h_drug_req
JOIN exp_header_type AS ht_drug ON h_drug_req.header_type_id = ht_drug.type_id AND ht_drug.type_code = 'YXIIT02'
WHERE h_drug_req.status = 'DRAFT';

-- 3. 测试状态过滤（多个状态）
SELECT 
    h_drug_req.status,
    COUNT(*) AS '数量'
FROM exp_claim_header AS h_drug_req
JOIN exp_header_type AS ht_drug ON h_drug_req.header_type_id = ht_drug.type_id AND ht_drug.type_code = 'YXIIT02'
WHERE h_drug_req.status IN ('DRAFT', 'SUBMIT', 'APPROVE')
GROUP BY h_drug_req.status;

-- 4. 测试状态统计
SELECT 
    h_drug_req.status AS '状态代码',
    CASE h_drug_req.status
        WHEN 'DRAFT' THEN '草稿'
        WHEN 'SUBMIT' THEN '已提交'
        WHEN 'APPROVE' THEN '已审批'
        WHEN 'REJECT' THEN '已驳回'
        WHEN 'CANCEL' THEN '已取消'
        WHEN 'CLOSE' THEN '已关闭'
        ELSE h_drug_req.status
    END AS '状态名称',
    COUNT(*) AS '数量'
FROM exp_claim_header AS h_drug_req
JOIN exp_header_type AS ht_drug ON h_drug_req.header_type_id = ht_drug.type_id AND ht_drug.type_code = 'YXIIT02'
WHERE h_drug_req.status != 'deleted'
GROUP BY h_drug_req.status
ORDER BY COUNT(*) DESC;

-- 5. 验证LOV关联是否正常
SELECT 
    h_drug_req.document_num AS '领药系统单号',
    h_drug_req.status AS '状态',
    iit_class_tl.value_meaning AS 'IIT分类',
    h_iit_req.column46 AS '项目号'
FROM exp_claim_header AS h_drug_req
JOIN exp_header_type AS ht_drug ON h_drug_req.header_type_id = ht_drug.type_id AND ht_drug.type_code = 'YXIIT02'
LEFT JOIN exp_claim_header AS h_iit_req ON h_drug_req.link_header_id = h_iit_req.header_id
LEFT JOIN fnd_lov AS iit_class_lov ON iit_class_lov.lov_name = 'categories'
LEFT JOIN fnd_lov_value AS iit_class_v ON iit_class_lov.lov_id = iit_class_v.lov_id AND h_iit_req.column15 = iit_class_v.value_code
LEFT JOIN fnd_lov_value_tl AS iit_class_tl ON iit_class_v.value_id = iit_class_tl.value_id AND iit_class_tl.language = 'zh_CN'
WHERE h_drug_req.status != 'deleted'
LIMIT 5;
