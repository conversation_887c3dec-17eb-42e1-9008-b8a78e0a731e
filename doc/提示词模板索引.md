# OA系统交互提示词模板集合

## 提示词模板索引

本文档提供了一系列用于OA系统交互的AI提示词模板，帮助您快速生成相关代码。

### 1. [OA系统表单处理专用提示词模板](OA系统表单处理专用提示词模板.md)

**用途**：根据OA表单字段对照表生成表单数据接收类或创建OA单据的代码。

**适用场景**：
- 需要接收OA系统发送的表单数据
- 需要向OA系统发送表单数据创建单据

**主要功能**：
- 生成表单数据POJO类
- 生成创建OA单据的方法

### 2. [Excel数据导入到OA系统提示词模板](Excel数据导入到OA系统提示词模板.md)

**用途**：从Excel文件读取数据，转换为POJO对象，并推送到OA系统。

**适用场景**：
- 批量从Excel导入数据并创建OA单据
- 需要将Excel数据映射到OA表单字段

**主要功能**：
- 生成Excel数据读取工具类
- 生成数据转换和推送方法

### 3. [系统间数据转换提示词模板](系统间数据转换提示词模板.md)

**用途**：将一个系统的数据转换为另一个系统（如OA系统）可接受的格式。

**适用场景**：
- 需要从一个系统向另一个系统推送数据
- 需要处理两个系统间的字段映射和数据转换

**主要功能**：
- 生成系统间数据转换服务
- 处理字段映射和类型转换

### 4. [系统集成中间层工作流程与提示词模板](系统集成中间层工作流程与提示词模板.md)

**用途**：为系统A（如OA系统）到第三方系统的集成中间层提供完整的工作流程和提示词模板集。

**适用场景**：
- 需要开发从源系统到目标系统的完整集成解决方案
- 需要系统地处理集成过程中的各种挑战

**主要功能**：
- 提供完整的系统集成工作流程图
- 提供8个专用提示词模板，覆盖从需求分析到监控部署的各个阶段
- 提供提示词模板选择决策树
- 提供最佳实践和使用示例

## 使用指南

1. 根据您的需求，选择合适的提示词模板
2. 复制模板内容，并根据实际情况填写相关信息
3. 将填写好的模板发送给AI，获取生成的代码
4. 根据需要调整和优化生成的代码

## 最佳实践

1. 提供完整准确的字段对照表或映射关系
2. 明确指出特殊处理需求和业务规则
3. 检查生成的代码是否符合项目规范和业务需求
4. 对于复杂场景，可能需要组合使用多个模板

## 自定义和扩展

您可以根据实际需求，对这些模板进行自定义和扩展，以适应更多的使用场景。如果有特殊需求，可以在提示词中明确说明，AI会尽可能满足您的要求。 