import request from '@/utils/request'

// 查询安全库存物料清单列表
export function listInventory(query) {
  return request({
    url: '/autoacct/inventory/list',
    method: 'get',
    params: query
  })
}

// 查询安全库存物料清单详细
export function getInventory(id) {
  return request({
    url: '/autoacct/inventory/' + id,
    method: 'get'
  })
}

// 新增安全库存物料清单
export function addInventory(data) {
  return request({
    url: '/autoacct/inventory',
    method: 'post',
    data: data
  })
}

// 修改安全库存物料清单
export function updateInventory(data) {
  return request({
    url: '/autoacct/inventory',
    method: 'put',
    data: data
  })
}

// 删除安全库存物料清单
export function delInventory(id) {
  return request({
    url: '/autoacct/inventory/' + id,
    method: 'delete'
  })
}
