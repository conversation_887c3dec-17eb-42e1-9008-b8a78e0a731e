import request from '@/utils/request'

// 查询MPM编码列表
export function listInfo(query) {
  return request({
    url: '/autoacct/mpm/list',
    method: 'get',
    params: query
  })
}

// 查询MPM编码详细
export function getInfo(id) {
  return request({
    url: '/autoacct/mpm/' + id,
    method: 'get'
  })
}

// 新增MPM编码
export function addInfo(data) {
  return request({
    url: '/autoacct/mpm',
    method: 'post',
    data: data
  })
}

// 修改MPM编码
export function updateInfo(data) {
  return request({
    url: '/autoacct/mpm',
    method: 'put',
    data: data
  })
}

// 删除MPM编码
export function delInfo(id) {
  return request({
    url: '/autoacct/mpm/' + id,
    method: 'delete'
  })
}
// 删除MPM编码
export function queryMpminfolList(data) {
  return request({
    url: '/autoacct/mpm/mpmlList/',
    method: 'post',
    data: data
  })
}
