import request from '@/utils/request'

// 查询公司运营费用数据列表
export function listCompanyOperatingExpenses(query) {
  return request({
    url: '/basicData/companyOperatingExpenses/list',
    method: 'get',
    params: query
  })
}

// 查询公司运营费用数据详细
export function getCompanyOperatingExpenses(id) {
  return request({
    url: '/basicData/companyOperatingExpenses/' + id,
    method: 'get'
  })
}

// 新增公司运营费用数据
export function addCompanyOperatingExpenses(data) {
  return request({
    url: '/basicData/companyOperatingExpenses',
    method: 'post',
    data: data
  })
}

// 修改公司运营费用数据
export function updateCompanyOperatingExpenses(data) {
  return request({
    url: '/basicData/companyOperatingExpenses',
    method: 'put',
    data: data
  })
}

// 删除公司运营费用数据
export function delCompanyOperatingExpenses(id) {
  return request({
    url: '/basicData/companyOperatingExpenses/' + id,
    method: 'delete'
  })
}
