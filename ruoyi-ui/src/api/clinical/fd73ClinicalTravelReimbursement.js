import request from '@/utils/request'

// 查询FD73临床差旅报销列表
export function listFd73ClinicalTravelReimbursement(query) {
  return request({
    url: '/clinical/fd73ClinicalTravelReimbursement/list',
    method: 'get',
    params: query
  })
}

// 查询FD73临床差旅报销详细
export function getFd73ClinicalTravelReimbursement(id) {
  return request({
    url: '/clinical/fd73ClinicalTravelReimbursement/' + id,
    method: 'get'
  })
}

// 新增FD73临床差旅报销
export function addFd73ClinicalTravelReimbursement(data) {
  return request({
    url: '/clinical/fd73ClinicalTravelReimbursement',
    method: 'post',
    data: data
  })
}

// 修改FD73临床差旅报销
export function updateFd73ClinicalTravelReimbursement(data) {
  return request({
    url: '/clinical/fd73ClinicalTravelReimbursement',
    method: 'put',
    data: data
  })
}

// 删除FD73临床差旅报销
export function delFd73ClinicalTravelReimbursement(id) {
  return request({
    url: '/clinical/fd73ClinicalTravelReimbursement/' + id,
    method: 'delete'
  })
}
