import request from '@/utils/request'

// 查询临床办公用品2024列表
export function listOffice(query) {
  return request({
    url: '/clinical/office/list',
    method: 'get',
    params: query
  })
}

// 查询临床办公用品2024详细
export function getOffice(id) {
  return request({
    url: '/clinical/office/' + id,
    method: 'get'
  })
}

// 新增临床办公用品2024
export function addOffice(data) {
  return request({
    url: '/clinical/office',
    method: 'post',
    data: data
  })
}

// 修改临床办公用品2024
export function updateOffice(data) {
  return request({
    url: '/clinical/office',
    method: 'put',
    data: data
  })
}

// 删除临床办公用品2024
export function delOffice(id) {
  return request({
    url: '/clinical/office/' + id,
    method: 'delete'
  })
}
