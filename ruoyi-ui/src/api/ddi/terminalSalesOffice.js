import request from '@/utils/request'

// 查询终端销售统计-办事处列表
export function listTerminalSalesOffice(query) {
  return request({
    url: '/ddi/terminalSalesOffice/list',
    method: 'get',
    params: query
  })
}

// 查询终端销售统计-办事处详细
export function getTerminalSalesOffice(id) {
  return request({
    url: '/ddi/terminalSalesOffice/' + id,
    method: 'get'
  })
}

// 新增终端销售统计-办事处
export function addTerminalSalesOffice(data) {
  return request({
    url: '/ddi/terminalSalesOffice',
    method: 'post',
    data: data
  })
}

// 修改终端销售统计-办事处
export function updateTerminalSalesOffice(data) {
  return request({
    url: '/ddi/terminalSalesOffice',
    method: 'put',
    data: data
  })
}

// 删除终端销售统计-办事处
export function delTerminalSalesOffice(id) {
  return request({
    url: '/ddi/terminalSalesOffice/' + id,
    method: 'delete'
  })
}
