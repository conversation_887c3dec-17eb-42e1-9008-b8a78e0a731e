import request from '@/utils/request'

// 查询终端销售统计表-药房销售列表
export function listTerminalSalesPharmacy(query) {
  return request({
    url: '/ddi/terminalSalesPharmacy/list',
    method: 'get',
    params: query
  })
}

// 查询终端销售统计表-药房销售详细
export function getTerminalSalesPharmacy(id) {
  return request({
    url: '/ddi/terminalSalesPharmacy/' + id,
    method: 'get'
  })
}

// 新增终端销售统计表-药房销售
export function addTerminalSalesPharmacy(data) {
  return request({
    url: '/ddi/terminalSalesPharmacy',
    method: 'post',
    data: data
  })
}

// 修改终端销售统计表-药房销售
export function updateTerminalSalesPharmacy(data) {
  return request({
    url: '/ddi/terminalSalesPharmacy',
    method: 'put',
    data: data
  })
}

// 删除终端销售统计表-药房销售
export function delTerminalSalesPharmacy(id) {
  return request({
    url: '/ddi/terminalSalesPharmacy/' + id,
    method: 'delete'
  })
}
