import request from '@/utils/request'

// 查询法务合同列表
export function listContractApproval(query) {
  return request({
    url: '/legal/contractApproval/list',
    method: 'get',
    params: query
  })
}

// 查询法务合同详细
export function getContractApproval(dateCreated) {
  return request({
    url: '/legal/contractApproval/' + dateCreated,
    method: 'get'
  })
}

// 新增法务合同
export function addContractApproval(data) {
  return request({
    url: '/legal/contractApproval',
    method: 'post',
    data: data
  })
}

// 修改法务合同
export function updateContractApproval(data) {
  return request({
    url: '/legal/contractApproval',
    method: 'put',
    data: data
  })
}

// 删除法务合同
export function delContractApproval(dateCreated) {
  return request({
    url: '/legal/contractApproval/' + dateCreated,
    method: 'delete'
  })
}
