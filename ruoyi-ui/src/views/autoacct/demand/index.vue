<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="核算代码" prop="mpmCode">
        <el-input
          v-model="queryParams.mpmCode"
          placeholder="请输入核算代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核算名称" prop="mpmName">
        <el-input
          v-model="queryParams.mpmName"
          placeholder="请输入核算名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产工序" prop="productionProcess">
        <el-input
          v-model="queryParams.productionProcess"
          placeholder="请输入生产工序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组件SAP代码" prop="componentSapCode">
        <el-input
          v-model="queryParams.componentSapCode"
          placeholder="请输入组件SAP代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组件描述" prop="componentDescription">
        <el-input
          v-model="queryParams.componentDescription"
          placeholder="请输入组件描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="使用单位" prop="unitOfUsage">
        <el-input
          v-model="queryParams.unitOfUsage"
          placeholder="请输入使用单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="批用量" prop="lotSize">
        <el-input
          v-model="queryParams.lotSize"
          placeholder="请输入批用量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料计划代码检查" prop="materialPlanningCodeCheck">
        <el-input
          v-model="queryParams.materialPlanningCodeCheck"
          placeholder="请输入物料计划代码检查"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['report:demand:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['report:demand:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['report:demand:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['report:demand:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
        >导入
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="demandList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="核算代码" align="center" prop="mpmCode" />
      <el-table-column label="核算名称" align="center" prop="mpmName" />
      <el-table-column label="生产工序" align="center" prop="productionProcess" />
      <el-table-column label="组件SAP代码" align="center" prop="componentSapCode" />
      <el-table-column label="组件描述" align="center" prop="componentDescription" />
      <el-table-column label="使用单位" align="center" prop="unitOfUsage" />
      <el-table-column label="批用量" align="center" prop="lotSize" />
      <el-table-column label="物料计划代码检查" align="center" prop="materialPlanningCodeCheck" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['report:demand:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['report:demand:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改BOM-物料需求对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="核算代码" prop="mpmCode">
          <el-input v-model="form.mpmCode" placeholder="请输入核算代码" />
        </el-form-item>
        <el-form-item label="核算名称" prop="mpmName">
          <el-input v-model="form.mpmName" placeholder="请输入核算名称" />
        </el-form-item>
        <el-form-item label="生产工序" prop="productionProcess">
          <el-input v-model="form.productionProcess" placeholder="请输入生产工序" />
        </el-form-item>
        <el-form-item label="组件SAP代码" prop="componentSapCode">
          <el-input v-model="form.componentSapCode" placeholder="请输入组件SAP代码" />
        </el-form-item>
        <el-form-item label="组件描述" prop="componentDescription">
          <el-input v-model="form.componentDescription" placeholder="请输入组件描述" />
        </el-form-item>
        <el-form-item label="使用单位" prop="unitOfUsage">
          <el-input v-model="form.unitOfUsage" placeholder="请输入使用单位" />
        </el-form-item>
        <el-form-item label="批用量" prop="lotSize">
          <el-input v-model="form.lotSize" placeholder="请输入批用量" />
        </el-form-item>
        <el-form-item label="物料计划代码检查" prop="materialPlanningCodeCheck">
          <el-input v-model="form.materialPlanningCodeCheck" placeholder="请输入物料计划代码检查" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport"/>
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
                   @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDemand, getDemand, delDemand, addDemand, updateDemand } from "@/api/autoacct/demand";
import {getToken} from "@/utils/auth";

export default {
  name: "Demand",
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/autoacct/demand/importData"
      },

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // BOM-物料需求表格数据
      demandList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mpmCode: null,
        mpmName: null,
        productionProcess: null,
        componentSapCode: null,
        componentDescription: null,
        unitOfUsage: null,
        lotSize: null,
        materialPlanningCodeCheck: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询BOM-物料需求列表 */
    getList() {
      this.loading = true;
      listDemand(this.queryParams).then(response => {
        this.demandList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        mpmCode: null,
        mpmName: null,
        productionProcess: null,
        componentSapCode: null,
        componentDescription: null,
        unitOfUsage: null,
        lotSize: null,
        materialPlanningCodeCheck: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.mpmCode)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加BOM-物料需求";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const mpmCode = row.mpmCode || this.ids
      getDemand(mpmCode).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改BOM-物料需求";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.mpmCode != null) {
            updateDemand(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDemand(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const mpmCodes = row.mpmCode || this.ids;
      this.$modal.confirm('是否确认删除BOM-物料需求编号为"' + mpmCodes + '"的数据项？').then(function() {
        return delDemand(mpmCodes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('autoacct/demand/export', {
        ...this.queryParams
      }, `demand_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('autoacct/demand/importTemplate', {}, `demand_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }

};
</script>
