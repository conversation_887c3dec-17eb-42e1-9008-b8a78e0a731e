<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="yearTime">
        <el-input
          v-model="queryParams.yearTime"
          placeholder="请输入年份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['basicData:financingSituation:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basicData:financingSituation:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basicData:financingSituation:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleImport"
          v-hasPermi="['basicData:financingSituation:import']"
        >导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['basicData:financingSituation:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="financingSituationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
<!--      <el-table-column label="ID" align="center" prop="id"/>-->
      <el-table-column label="公司名称" align="center" prop="companyName" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="公司简称" align="center" prop="companyAbbreviation" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="公司代码" align="center" prop="companyCode" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="年份" align="center" prop="yearTime" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="业务类型" align="center" prop="businessType" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="授信敞口" align="center" prop="creditExposure" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="合同签订金额" align="center" prop="contractSigningAmount" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="提款情况" align="center" prop="withdrawalSituation" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="还款情况" align="center" prop="repaymentSituation" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="贷款余额" align="center" prop="loanBalance" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basicData:financingSituation:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basicData:financingSituation:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 融资情况数据导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <!--          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据-->
          <el-link type="info"  style="font-size:20px;color:red" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:#0040ff" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改融资情况对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入公司名称"/>
        </el-form-item>
        <el-form-item label="公司简称" prop="companyAbbreviation">
          <el-input v-model="form.companyAbbreviation" placeholder="请输入公司简称"/>
        </el-form-item>
        <el-form-item label="公司代码" prop="companyCode">
          <el-input v-model="form.companyCode" placeholder="请输入公司代码"/>
        </el-form-item>
        <el-form-item label="年份" prop="yearTime">
          <el-input v-model="form.yearTime" placeholder="请输入年份"/>
        </el-form-item>
        <el-form-item label="授信敞口" prop="creditExposure">
          <el-input v-model="form.creditExposure" placeholder="请输入授信敞口"/>
        </el-form-item>
        <el-form-item label="合同签订金额" prop="contractSigningAmount">
          <el-input v-model="form.contractSigningAmount" placeholder="请输入合同签订金额"/>
        </el-form-item>
        <el-form-item label="提款情况" prop="withdrawalSituation">
          <el-input v-model="form.withdrawalSituation" placeholder="请输入提款情况"/>
        </el-form-item>
        <el-form-item label="还款情况" prop="repaymentSituation">
          <el-input v-model="form.repaymentSituation" placeholder="请输入还款情况"/>
        </el-form-item>
        <el-form-item label="贷款余额" prop="loanBalance">
          <el-input v-model="form.loanBalance" placeholder="请输入贷款余额" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFinancingSituation,
  getFinancingSituation,
  delFinancingSituation,
  addFinancingSituation,
  updateFinancingSituation
} from "@/api/basicData/financingSituation";
import {getToken} from "@/utils/auth";

export default {
  name: "FinancingSituation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 融资情况表格数据
      financingSituationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
        companyAbbreviation: null,
        companyCode: null,
        yearTime: null,
        businessType: null,
        creditExposure: null,
        contractSigningAmount: null,
        withdrawalSituation: null,
        repaymentSituation: null,
        deleteStatus: null,
        loanBalance: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/basicData/financingSituation/importData"
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询融资情况列表 */
    getList() {
      this.loading = true;
      listFinancingSituation(this.queryParams).then(response => {
        this.financingSituationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyName: null,
        companyAbbreviation: null,
        companyCode: null,
        yearTime: null,
        businessType: null,
        creditExposure: null,
        contractSigningAmount: null,
        withdrawalSituation: null,
        repaymentSituation: null,
        deleteStatus: null,
        loanBalance: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加融资情况";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFinancingSituation(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改融资情况";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFinancingSituation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFinancingSituation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除融资情况编号为"' + ids + '"的数据项？').then(function () {
        return delFinancingSituation(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "融资情况数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      var date = new Date()
      var year = date.getFullYear().toString()
      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('/basicData/financingSituation/importTemplate', {}, `融资情况数据导入模板_${dateTime}.xlsx`)
    },
// 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
// 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
    },
// 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('basicData/financingSituation/export', {
        ...this.queryParams
      }, `financingSituation_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
