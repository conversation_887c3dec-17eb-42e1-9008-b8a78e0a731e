<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="产品名称">
        <el-select v-model="queryParams.standardProductName" placeholder="请选择">
          <el-option label="开坦尼" value="开坦尼"></el-option>
          <el-option label="依达方" value="依达方"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="商业级别" prop="commercialGrade">
        <el-input
          v-model="queryParams.commercialGrade"
          placeholder="请输入商业级别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="商业省份" prop="commercialProvince">
        <el-input
          v-model="queryParams.commercialProvince"
          placeholder="请输入商业省份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item label="商业编码" prop="businessCoding">
        <el-input
          v-model="queryParams.businessCoding"
          placeholder="请输入商业编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商业名称" prop="businessName">
        <el-input
          v-model="queryParams.businessName"
          placeholder="请输入商业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户编码" prop="customerCode">
        <el-input
          v-model="queryParams.customerCode"
          placeholder="请输入客户编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="二级部门" prop="secondaryDepartment">
        <el-input
          v-model="queryParams.secondaryDepartment"
          placeholder="请输入二级部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="三级部门" prop="thirdLevelDepartments">
        <el-input
          v-model="queryParams.thirdLevelDepartments"
          placeholder="请输入三级部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="四级部门" prop="fourthLevelDepartment">
        <el-input
          v-model="queryParams.fourthLevelDepartment"
          placeholder="请输入四级部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="五级部门" prop="fifthLevelDepartment">
        <el-input
          v-model="queryParams.fifthLevelDepartment"
          placeholder="请输入五级部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->

      <el-form-item label="是否目标终端" prop="isTargetTerminal">
        <el-input
          v-model="queryParams.isTargetTerminal"
          placeholder="请输入是否目标终端"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="出库日期">
        <el-date-picker
          v-model="daterangeShipmentDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ddi:ddiPharmacyDaySale2025:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ddiPharmacyDaySale2025List" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
<!--      <el-table-column label="主键id" align="center" prop="id"/>-->
      <el-table-column label="执行月" align="center" prop="executionMonth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务月" align="center" prop="businessMonth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="二级部门" align="center" prop="secondaryDepartment" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="三级部门" align="center" prop="thirdLevelDepartments" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="四级部门" align="center" prop="fourthLevelDepartment" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="五级部门" align="center" prop="fifthLevelDepartment" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="商业级别" align="center" prop="commercialGrade" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业省份" align="center" prop="commercialProvince" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业编码" align="center" prop="businessCoding" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="商业名称" align="center" prop="businessName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="出库日期" align="center" prop="shipmentDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.shipmentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户编码" align="center" prop="customerCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="客户名称" align="center" prop="customerName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品编码" align="center" prop="standardProductCodes" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品名称" align="center" prop="standardProductName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准产品规格" align="center" prop="standardProductSpecification" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准单位" align="center" prop="standardUnits" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准数量" align="center" prop="standardQuantity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="单价" align="center" prop="unitPrice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="金额" align="center" prop="amount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="标准批号" align="center" prop="standardLotNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="下游供货商编码" align="center" prop="downstreamVendorCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="下游供货商名称" align="center" prop="downstreamVendorName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="是否目标终端" align="center" prop="isTargetTerminal" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="下游收货方地址" align="center" prop="downstreamShipAddress" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品编码" align="center" prop="productCode" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品名称" align="center" prop="productName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品规格" align="center" prop="productSpecifications" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="生产厂家" align="center" prop="manufacturer" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量单位" align="center" prop="quantityUnits" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="quantity" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品单价" align="center" prop="productUnitPrice" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品金额" align="center" prop="productAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品批号" align="center" prop="productLotNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="订单编号" align="center" prop="orderNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="患者(会员)ID" align="center" prop="menberId" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="效期" align="center" prop="expirationDate" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批准文号" align="center" prop="approvalNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="适应症" align="center" prop="indications" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数据类型" align="center" prop="dataType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="运维状态" align="center" prop="maintenanceStatus" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {
  listDdiPharmacyDaySale2025
} from "@/api/ddi/ddiPharmacyDaySale2025";

export default {
  name: "DdiPharmacyDaySale2025",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 药房日销售表格数据
      ddiPharmacyDaySale2025List: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除状态时间范围
      daterangeShipmentDate: [],
      // 删除状态时间范围
      daterangeExpirationDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        executionMonth: null,
        businessMonth: null,
        businessCoding: null,
        businessName: null,
        shipmentDate: null,
        customerCode: null,
        customerName: null,
        standardProductCodes: null,
        standardProductName: '开坦尼',
        standardProductSpecification: null,
        standardUnits: null,
        standardQuantity: null,
        unitPrice: null,
        amount: null,
        standardLotNumber: null,
        downstreamVendorCode: null,
        downstreamVendorName: null,
        downstreamShipAddress: null,
        productCode: null,
        productName: null,
        productSpecifications: null,
        manufacturer: null,
        quantityUnits: null,
        quantity: null,
        productUnitPrice: null,
        productAmount: null,
        productLotNumber: null,
        orderNumber: null,
        menberId: null,
        expirationDate: null,
        approvalNumber: null,
        indications: null,
        dataType: null,
        maintenanceStatus: null,
        creationTime: null,
        updated: null,
        deletionTime: null,
        deleteStatus: null,
        commercialGrade: null,
        commercialProvince: null,
        secondaryDepartment: null,
        thirdLevelDepartments: null,
        fourthLevelDepartment: null,
        fifthLevelDepartment: null,
        isTargetTerminal: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询药房日销售列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeShipmentDate && '' != this.daterangeShipmentDate) {
        this.queryParams.params["beginShipmentDate"] = this.daterangeShipmentDate[0];
        this.queryParams.params["endShipmentDate"] = this.daterangeShipmentDate[1];
      }
      if (null != this.daterangeExpirationDate && '' != this.daterangeExpirationDate) {
        this.queryParams.params["beginExpirationDate"] = this.daterangeExpirationDate[0];
        this.queryParams.params["endExpirationDate"] = this.daterangeExpirationDate[1];
      }
      listDdiPharmacyDaySale2025(this.queryParams).then(response => {
        this.ddiPharmacyDaySale2025List = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        executionMonth: null,
        businessMonth: null,
        businessCoding: null,
        businessName: null,
        shipmentDate: null,
        customerCode: null,
        customerName: null,
        standardProductCodes: null,
        standardProductName: '开坦尼',
        standardProductSpecification: null,
        standardUnits: null,
        standardQuantity: null,
        unitPrice: null,
        amount: null,
        standardLotNumber: null,
        downstreamVendorCode: null,
        downstreamVendorName: null,
        downstreamShipAddress: null,
        productCode: null,
        productName: null,
        productSpecifications: null,
        manufacturer: null,
        quantityUnits: null,
        quantity: null,
        productUnitPrice: null,
        productAmount: null,
        productLotNumber: null,
        orderNumber: null,
        menberId: null,
        expirationDate: null,
        approvalNumber: null,
        indications: null,
        dataType: null,
        maintenanceStatus: null,
        creationTime: null,
        updated: null,
        deletionTime: null,
        deleteStatus: null,
        commercialGrade: null,
        commercialProvince: null,
        secondaryDepartment: null,
        thirdLevelDepartments: null,
        fourthLevelDepartment: null,
        fifthLevelDepartment: null,
        isTargetTerminal: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeShipmentDate = [];
      this.daterangeExpirationDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ddi/ddiPharmacyDaySale2025/export', {
        ...this.queryParams
      }, `ddiPharmacyDaySale2025_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
