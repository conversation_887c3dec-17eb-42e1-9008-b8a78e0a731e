<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="产品名称">
        <el-select v-model="queryParams.productName" placeholder="请选择">
          <el-option label="开坦尼" value="开坦尼"></el-option>
          <el-option label="依达方" value="依达方"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="销售日期">
        <el-date-picker
          v-model="queryParams.salesDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择销售日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="是否目标终端">
        <el-select v-model="queryParams.isTargetTerminal" placeholder="请选择">
          <el-option label="" value=""></el-option>
          <el-option label="是" value="是"></el-option>
          <el-option label="否" value="否"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ddi:terminalSalesHospital:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="terminalSalesHospitalList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <!--      <el-table-column label="ID" align="center" prop="id" />-->
      <!--      <el-table-column label="销售区域" align="center" prop="salesArea" width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="产品名称" align="center" prop="productName" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="销售大区" align="center" prop="salesRegion" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="销售省份" align="center" prop="salesProvince" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="销售日期" align="center" prop="salesDate" width="100" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.salesDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否目标终端" align="center" prop="isTargetTerminal" width="120" :show-overflow-tooltip="true"/>
      <el-table-column label="日销量" align="center" prop="dailySalesVolume" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="日销量金额(万元)" align="center" prop="dailySalesVolumeAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="日排名" align="center" prop="dailyRanking" width="100" :show-overflow-tooltip="true"/>-->
      <el-table-column label="日环比增长率" align="center" prop="dailyGrowthRate" width="150" :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="周销量" align="center" prop="weeklySalesVolume"   width="100" :show-overflow-tooltip="true"/>
            <el-table-column label="周排名" align="center" prop="weeklyRanking"   width="100" :show-overflow-tooltip="true"/>
            <el-table-column label="周环比增长率" align="center" prop="weeklyGrowthRate"   width="150" :show-overflow-tooltip="true"/>-->
      <el-table-column label="月销量" align="center" prop="monthlySalesVolume" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="月销量金额(万元)" align="center" prop="monthlySalesVolumeAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="月排名" align="center" prop="monthlyRanking" width="100" :show-overflow-tooltip="true"/>-->
      <el-table-column label="月环比增长率" align="center" prop="monthlyGrowthRate" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="季度销量" align="center" prop="quarterlySales" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="季度销量金额(万元)" align="center" prop="quarterlySalesAmount" width="150"
                       :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="季度排名" align="center" prop="quarterlyRanking" width="100" :show-overflow-tooltip="true"/>-->
      <el-table-column label="季度环比增长率" align="center" prop="quarterlyGrowthRate" width="150"
                       :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="24年累计销量" align="center" prop="salesVolumeYear24" width="150"
                             :show-overflow-tooltip="true"/>
            <el-table-column label="24年累计销量金额(万元)" align="center" prop="salesVolumeAmountYear24" width="200"
                             :show-overflow-tooltip="true"/>-->
      <!--      <el-table-column label="24年排名" align="center" prop="rankingYear24" width="100" :show-overflow-tooltip="true"/>-->
      <el-table-column label="25年累计销量" align="center" prop="salesVolumeYear25" width="100"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="25年累计销量金额(万元)" align="center" prop="salesVolumeAmountYear25" width="200"
                       :show-overflow-tooltip="true"/>
      <!--      <el-table-column label="上市后累计销量" align="center" prop="salesAfterListing" width="150"
                             :show-overflow-tooltip="true"/>
            <el-table-column label="上市后累计销量金额(万元)" align="center" prop="salesAfterListingAmount" width="200"
                             :show-overflow-tooltip="true"/>-->
      <!--      <el-table-column label="上市后累计销量排名" align="center" prop="salesRankingAfterListing" width="150"-->
      <!--                       :show-overflow-tooltip="true"/>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listTerminalSalesHospital,
  getTerminalSalesHospital,
  delTerminalSalesHospital,
  addTerminalSalesHospital,
  updateTerminalSalesHospital
} from "@/api/ddi/terminalSalesHospital";

export default {
  name: "TerminalSalesHospital",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 终端销售统计表-医院购进表格数据
      terminalSalesHospitalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        salesArea: null,
        salesRegion: null,
        salesProvince: null,
        salesDate: null,
        dailySalesVolume: null,
        dailyRanking: null,
        dailyGrowthRate: null,
        weeklySalesVolume: null,
        weeklyRanking: null,
        weeklyGrowthRate: null,
        monthlySalesVolume: null,
        monthlyRanking: null,
        monthlyGrowthRate: null,
        salesVolumeYear24: null,
        rankingYear24: null,
        salesAfterListing: null,
        salesRankingAfterListing: null,
        deleteStatus: null,
        quarterlySales: null,
        quarterlyRanking: null,
        quarterlyGrowthRate: null,
        productName: '开坦尼',
        productCode: null,
        dailySalesVolumeAmount: null,
        monthlySalesVolumeAmount: null,
        quarterlySalesAmount: null,
        salesVolumeAmountYear24: null,
        salesAfterListingAmount: null,
        salesVolumeYear25: null,
        salesVolumeAmountYear25: null,
        isTargetTerminal: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getDate();
    this.getList();
  },
  methods: {
    getDate() {
      let date = this.$moment(new Date().setDate(new Date().getDate() - 2)).format('YYYY-MM-DD');
      this.queryParams.salesDate = date
    },
    /** 查询终端销售统计表-医院购进列表 */
    getList() {
      this.loading = true;
      listTerminalSalesHospital(this.queryParams).then(response => {
        this.terminalSalesHospitalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        salesArea: null,
        salesRegion: null,
        salesProvince: null,
        salesDate: null,
        dailySalesVolume: null,
        dailyRanking: null,
        dailyGrowthRate: null,
        weeklySalesVolume: null,
        weeklyRanking: null,
        weeklyGrowthRate: null,
        monthlySalesVolume: null,
        monthlyRanking: null,
        monthlyGrowthRate: null,
        salesVolumeYear24: null,
        rankingYear24: null,
        salesAfterListing: null,
        salesRankingAfterListing: null,
        deleteStatus: null,
        quarterlySales: null,
        quarterlyRanking: null,
        quarterlyGrowthRate: null,
        productName: '开坦尼',
        productCode: null,
        dailySalesVolumeAmount: null,
        monthlySalesVolumeAmount: null,
        quarterlySalesAmount: null,
        salesVolumeAmountYear24: null,
        salesAfterListingAmount: null,
        salesVolumeYear25: null,
        salesVolumeAmountYear25: null,
        isTargetTerminal: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加终端销售统计表-医院购进";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTerminalSalesHospital(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改终端销售统计表-医院购进";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTerminalSalesHospital(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTerminalSalesHospital(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除终端销售统计表-医院购进编号为"' + ids + '"的数据项？').then(function () {
        return delTerminalSalesHospital(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ddi/terminalSalesHospital/export', {
        ...this.queryParams
      }, `terminalSalesHospital_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
