<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="主体" prop="mainBody">
        <el-select v-model="queryParams.mainBody" style="width: 120px">
          <el-option
            v-for="item in mainBodyOption"
            :key="item.label"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级部门" prop="department">
        <el-input
          v-model="queryParams.department"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['hr:pm:manpower-structure:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

        <el-table v-loading="loading" border :max-height="tableMaxHeight" :data="structureList" :span-method="mergeMainBody"
                  @selection-change="handleSelectionChange" show-summary :summary-method="getSummaries">
          <el-table-column label="一级部门" key="1" align="center" prop="department"
                           v-if="realQueryParams.mainBody === 1 && columns[1].visible"
                           width="120" :show-overflow-tooltip='true' fixed/>
          <el-table-column label="公司" key="2" align="center" prop="company"
                           v-if="realQueryParams.mainBody === 1 && columns[0].visible"
                           width="100" :show-overflow-tooltip='true' fixed/>
          <el-table-column label="公司" key="3" align="center" prop="company"
                           v-if="realQueryParams.mainBody === 0 && columns[0].visible"
                           width="100" :show-overflow-tooltip='true' fixed/>
          <el-table-column label="一级部门" key="4" align="center" prop="department"
                           v-if="realQueryParams.mainBody === 0 && columns[1].visible"
                           width="120" :show-overflow-tooltip='true' fixed/>
          <el-table-column label="在职人数" align="center" prop="realtime" v-if="columns[2].visible" width="80"
                           :show-overflow-tooltip='true' fixed/>
          <el-table-column label="实习生" align="center" prop="intern" v-if="columns[3].visible" width="80"
                           :show-overflow-tooltip='true' fixed/>
          <el-table-column label="上年末总人数" align="center" prop="lastTime" v-if="columns[4].visible" width="80"
                           :show-overflow-tooltip='true' fixed/>
          <el-table-column label="月累计增加人数" align="center" prop="increase" v-if="columns[5].visible" width="80"
                           :show-overflow-tooltip='true' fixed/>
          <el-table-column label="月累计减少人数" align="center" prop="decrease" v-if="columns[6].visible" width="80"
                           :show-overflow-tooltip='true' fixed/>

          <el-table-column label="知识结构(人数)" align="center" v-if="columns[7].visible">
            <el-table-column label="博士" align="center" prop="doctor" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="硕士" align="center" prop="master" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="本科" align="center" prop="bachelor" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="大专" align="center" prop="associateDegree" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="其它" align="center" prop="other" width="100" :show-overflow-tooltip='true'/>
          </el-table-column>

          <el-table-column label="年龄结构(人数)" align="center" v-if="columns[8].visible">
            <el-table-column label="25岁以下" align="center" prop="under25" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="25-35岁" align="center" prop="age25To35" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="35-45岁" align="center" prop="age35To45" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="45-55岁" align="center" prop="age45To55" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="55岁以上" align="center" prop="over55" width="100" :show-overflow-tooltip='true'/>
          </el-table-column>

          <el-table-column label="司龄结构(人数)" align="center" v-if="columns[9].visible">
            <el-table-column label="1年以下" align="center" prop="below1" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="1-3年" align="center" prop="year1To3" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="3-5年" align="center" prop="year3To5" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="5-8年" align="center" prop="year5To8" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="8-15年" align="center" prop="year8To15" width="100" :show-overflow-tooltip='true'/>
          </el-table-column>

          <el-table-column label="性别结构(人数)" align="center" v-if="columns[10].visible">
            <el-table-column label="男" align="center" prop="male" width="100" :show-overflow-tooltip='true'/>
            <el-table-column label="女" align="center" prop="female" width="100" :show-overflow-tooltip='true'/>
          </el-table-column>
        </el-table>
  </div>
</template>

<script>
import {queryManpowerStructure} from "@/api/hr/personnel/manpowerStructure";

export default {
  name: "ManpowerStructure",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 实际显示的结构数据
      responseStructureList: [],
      // 实际显示的结构数据
      structureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 入职日期时间范围
      dateRangeEntryDate: [],
      // 转正日期时间范围
      dateRangePositiveDate: [],
      // 合同到期日期时间范围
      dateRangeContractDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        department: null,
        company: null,
        mainBody: 0
      },
      // 页面结果当前查询参数(处理结果用)
      realQueryParams: {
        department: null,
        company: null,
        mainBody: 0
      },
      mainBodyOption: [
        {"label": '公司', "value": 0},
        {"label": '部门', "value": 1},
      ],
      // 合并行的行数组信息
      spanArr: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 列信息
      columns: [
        {key: 0, label: `公司`, visible: true},
        {key: 1, label: `一级部门`, visible: true},
        {key: 2, label: `在职人数`, visible: true},
        {key: 3, label: `实习生`, visible: true},
        {key: 4, label: `上年末总人数`, visible: true},
        {key: 5, label: `月累计增加人数`, visible: true},
        {key: 6, label: `月累计减少人数`, visible: true},
        {key: 7, label: `知识结构`, visible: true},
        {key: 8, label: `年龄结构`, visible: true},
        {key: 9, label: `司龄结构`, visible: true},
        {key: 10, label: `性别结构`, visible: true}
      ],
    };
  },
  created() {
    this.getList();
  },
  computed: {
    tableMaxHeight() {
      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      return screenHeight - 170;
    }
  },
  watch: {
    "columns": {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        this.handlerSubBody();
      },
    },
    "responseStructureList": {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        this.handlerSubBody();
      },
    }
  },
  methods: {
    /** 查询培训台账 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      queryManpowerStructure(this.queryParams).then(response => {
        this.responseStructureList = response;
        this.responseStructureList = this.responseStructureList.filter(item => item.company !== "总数" && item.department !== "总数")
        this.getSpanArr(this.responseStructureList)
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        sex: null,
        idCard: null,
        birthday: null,
        email: null,
        entryDate: null,
        status: null,
        mobile: null,
        orgId: null,
        deptId: null,
        loginName: null,
        post: null,
        orgName: null,
        deptName: null,
        deptNameAll: null,
        jobGrade: null
      };
      this.resetForm("form");
    },
    isBlank(value) {
      return value === null || value === undefined || value === '';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.isBlank(this.queryParams.company) && this.queryParams.department) {
        this.realQueryParams.mainBody = 1
        this.realQueryParams.company = null
        this.realQueryParams.department = this.queryParams.department
      } else if (this.queryParams.company && this.isBlank(this.queryParams.department)) {
        this.realQueryParams.mainBody = 0
        this.realQueryParams.company = this.queryParams.company
        this.realQueryParams.department = null
      } else {
        this.realQueryParams.mainBody = this.queryParams.mainBody
        this.realQueryParams.company = this.queryParams.company
        this.realQueryParams.department = this.queryParams.department
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      let date = new Date()
      let year = date.getFullYear().toString()
      let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
      let day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
      let h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()
      let m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()
      let s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()
      let dateTime = year + '-' + month + '-' + day + "_" + h + m + s
      this.download('hr/personnel/manpower-structure/export', {
        ...this.queryParams
      }, `人力结构_${dateTime}.xlsx`)
    },
    // 合并相同的主体
    mergeMainBody({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row, //行
          colspan: _col //列
        };
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (this.realQueryParams.mainBody === 0) {
            if (data[i].company === data[i - 1].company && data[i].company) {
              this.spanArr[this.pos] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.pos = i;
            }
          } else {
            if (data[i].department === data[i - 1].department && data[i].department) {
              this.spanArr[this.pos] += 1;
              this.spanArr.push(0);
            } else {
              this.spanArr.push(1);
              this.pos = i;
            }
          }
        }
      }
    },
    // 处理隐藏公司或者部门显示时只显示总数
    handlerSubBody() {
      let companyVisible = this.columns[0].visible
      let departmentVisible = this.columns[1].visible
      if (this.realQueryParams.mainBody === 0) {
        if (departmentVisible || (!companyVisible && !departmentVisible)) {
          this.structureList = this.responseStructureList
          this.getSpanArr(this.structureList)
        } else {
          this.structureList = this.responseStructureList.filter(item => item.department === '汇总')
          this.getSpanArr(this.structureList)
        }
      } else {
        if (companyVisible || (!companyVisible && !departmentVisible)) {
          this.structureList = this.responseStructureList
          this.getSpanArr(this.structureList)
        } else {
          this.structureList = this.responseStructureList.filter(item => item.company === '汇总')
          this.getSpanArr(this.structureList)
        }
      }
    },
    // 合计
    getSummaries(param) {
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总数';
          return;
        }
        let values;
        if (this.realQueryParams.mainBody === 0) {
          values = data.filter(item => item.department === '汇总').map(item => Number(item[column.property]));
        } else {
          values = data.filter(item => item.company === '汇总').map(item => Number(item[column.property]));
        }
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += '';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
  }
};
</script>
