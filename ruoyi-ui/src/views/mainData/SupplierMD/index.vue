<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="业务伙伴编码" prop="partner">
        <el-input
          v-model="queryParams.partner"
          placeholder="请输入业务伙伴编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="银行明细标识" prop="bkvid">
        <el-input
          v-model="queryParams.bkvid"
          placeholder="请输入银行明细标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务伙伴名称" prop="nameOrg1">
        <el-input
          v-model="queryParams.nameOrg1"
          placeholder="请输入业务伙伴名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="搜索词1" prop="buSort1">
        <el-input
          v-model="queryParams.buSort1"
          placeholder="请输入搜索词1"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务伙伴分组" prop="buGroup">
        <el-input
          v-model="queryParams.buGroup"
          placeholder="请输入业务伙伴分组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="社会统一信用代码" prop="idnumber">
        <el-input
          v-model="queryParams.idnumber"
          placeholder="请输入社会统一信用代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <el-input
          v-model="queryParams.country"
          placeholder="请输入国家"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="语言" prop="langu">
        <el-input
          v-model="queryParams.langu"
          placeholder="请输入语言"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="时区" prop="timeZone">
        <el-input
          v-model="queryParams.timeZone"
          placeholder="请输入时区"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="街道" prop="steet">
        <el-input
          v-model="queryParams.steet"
          placeholder="请输入街道"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话" prop="telNumber">
        <el-input
          v-model="queryParams.telNumber"
          placeholder="请输入电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="移动电话" prop="mobNumber">
        <el-input
          v-model="queryParams.mobNumber"
          placeholder="请输入移动电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="E-mail" prop="smtpAddr">
        <el-input
          v-model="queryParams.smtpAddr"
          placeholder="请输入E-mail"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统驭科目" prop="akont">
        <el-input
          v-model="queryParams.akont"
          placeholder="请输入统驭科目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="删除标志" prop="loevm">
        <el-input
          v-model="queryParams.loevm"
          placeholder="请输入删除标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="银行国家" prop="banks">
        <el-input
          v-model="queryParams.banks"
          placeholder="请输入银行国家"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="银行代码" prop="bankl">
        <el-input
          v-model="queryParams.bankl"
          placeholder="请输入银行代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户持有人" prop="koinh">
        <el-input
          v-model="queryParams.koinh"
          placeholder="请输入账户持有人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="银行帐号" prop="bankn">
        <el-input
          v-model="queryParams.bankn"
          placeholder="请输入银行帐号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="控制代码" prop="bkont">
        <el-input
          v-model="queryParams.bkont"
          placeholder="请输入控制代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认开票银行账号" prop="xezer">
        <el-input
          v-model="queryParams.xezer"
          placeholder="请输入默认开票银行账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="银行账户名称" prop="accname">
        <el-input
          v-model="queryParams.accname"
          placeholder="请输入银行账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否有效" prop="efficient">
        <el-input
          v-model="queryParams.efficient"
          placeholder="请输入是否有效"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新时间" prop="updatetime">
        <el-date-picker clearable
          v-model="queryParams.updatetime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="银行名称" prop="banka">
        <el-input
          v-model="queryParams.banka"
          placeholder="请输入银行名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="营销供应商标识" prop="zsfyxgys">
        <el-input
          v-model="queryParams.zsfyxgys"
          placeholder="请输入营销供应商标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="营销类别" prop="zyxlb">
        <el-input
          v-model="queryParams.zyxlb"
          placeholder="请输入营销类别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mainData:SupplierMD:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mainData:SupplierMD:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mainData:SupplierMD:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mainData:SupplierMD:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="SupplierMDList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键id" align="center" prop="sid" />
      <el-table-column label="业务伙伴编码" align="center" prop="partner" />
      <el-table-column label="银行明细标识" align="center" prop="bkvid" />
      <el-table-column label="业务伙伴名称" align="center" prop="nameOrg1" />
      <el-table-column label="搜索词1" align="center" prop="buSort1" />
      <el-table-column label="业务伙伴分组" align="center" prop="buGroup" />
      <el-table-column label="社会统一信用代码" align="center" prop="idnumber" />
      <el-table-column label="国家" align="center" prop="country" />
      <el-table-column label="语言" align="center" prop="langu" />
      <el-table-column label="时区" align="center" prop="timeZone" />
      <el-table-column label="街道" align="center" prop="steet" />
      <el-table-column label="电话" align="center" prop="telNumber" />
      <el-table-column label="移动电话" align="center" prop="mobNumber" />
      <el-table-column label="E-mail" align="center" prop="smtpAddr" />
      <el-table-column label="统驭科目" align="center" prop="akont" />
      <el-table-column label="删除标志" align="center" prop="loevm" />
      <el-table-column label="银行国家" align="center" prop="banks" />
      <el-table-column label="银行代码" align="center" prop="bankl" />
      <el-table-column label="账户持有人" align="center" prop="koinh" />
      <el-table-column label="银行帐号" align="center" prop="bankn" />
      <el-table-column label="控制代码" align="center" prop="bkont" />
      <el-table-column label="默认开票银行账号" align="center" prop="xezer" />
      <el-table-column label="银行账户名称" align="center" prop="accname" />
      <el-table-column label="是否有效" align="center" prop="efficient" />
      <el-table-column label="更新时间" align="center" prop="updatetime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updatetime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="银行名称" align="center" prop="banka" />
      <el-table-column label="营销供应商标识" align="center" prop="zsfyxgys" />
      <el-table-column label="营销类别" align="center" prop="zyxlb" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mainData:SupplierMD:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mainData:SupplierMD:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改供应商主数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="业务伙伴编码" prop="partner">
          <el-input v-model="form.partner" placeholder="请输入业务伙伴编码" />
        </el-form-item>
        <el-form-item label="银行明细标识" prop="bkvid">
          <el-input v-model="form.bkvid" placeholder="请输入银行明细标识" />
        </el-form-item>
        <el-form-item label="业务伙伴名称" prop="nameOrg1">
          <el-input v-model="form.nameOrg1" placeholder="请输入业务伙伴名称" />
        </el-form-item>
        <el-form-item label="搜索词1" prop="buSort1">
          <el-input v-model="form.buSort1" placeholder="请输入搜索词1" />
        </el-form-item>
        <el-form-item label="业务伙伴分组" prop="buGroup">
          <el-input v-model="form.buGroup" placeholder="请输入业务伙伴分组" />
        </el-form-item>
        <el-form-item label="社会统一信用代码" prop="idnumber">
          <el-input v-model="form.idnumber" placeholder="请输入社会统一信用代码" />
        </el-form-item>
        <el-form-item label="国家" prop="country">
          <el-input v-model="form.country" placeholder="请输入国家" />
        </el-form-item>
        <el-form-item label="语言" prop="langu">
          <el-input v-model="form.langu" placeholder="请输入语言" />
        </el-form-item>
        <el-form-item label="时区" prop="timeZone">
          <el-input v-model="form.timeZone" placeholder="请输入时区" />
        </el-form-item>
        <el-form-item label="街道" prop="steet">
          <el-input v-model="form.steet" placeholder="请输入街道" />
        </el-form-item>
        <el-form-item label="电话" prop="telNumber">
          <el-input v-model="form.telNumber" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="移动电话" prop="mobNumber">
          <el-input v-model="form.mobNumber" placeholder="请输入移动电话" />
        </el-form-item>
        <el-form-item label="E-mail" prop="smtpAddr">
          <el-input v-model="form.smtpAddr" placeholder="请输入E-mail" />
        </el-form-item>
        <el-form-item label="统驭科目" prop="akont">
          <el-input v-model="form.akont" placeholder="请输入统驭科目" />
        </el-form-item>
        <el-form-item label="删除标志" prop="loevm">
          <el-input v-model="form.loevm" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="银行国家" prop="banks">
          <el-input v-model="form.banks" placeholder="请输入银行国家" />
        </el-form-item>
        <el-form-item label="银行代码" prop="bankl">
          <el-input v-model="form.bankl" placeholder="请输入银行代码" />
        </el-form-item>
        <el-form-item label="账户持有人" prop="koinh">
          <el-input v-model="form.koinh" placeholder="请输入账户持有人" />
        </el-form-item>
        <el-form-item label="银行帐号" prop="bankn">
          <el-input v-model="form.bankn" placeholder="请输入银行帐号" />
        </el-form-item>
        <el-form-item label="控制代码" prop="bkont">
          <el-input v-model="form.bkont" placeholder="请输入控制代码" />
        </el-form-item>
        <el-form-item label="默认开票银行账号" prop="xezer">
          <el-input v-model="form.xezer" placeholder="请输入默认开票银行账号" />
        </el-form-item>
        <el-form-item label="银行账户名称" prop="accname">
          <el-input v-model="form.accname" placeholder="请输入银行账户名称" />
        </el-form-item>
        <el-form-item label="是否有效" prop="efficient">
          <el-input v-model="form.efficient" placeholder="请输入是否有效" />
        </el-form-item>
        <el-form-item label="银行名称" prop="banka">
          <el-input v-model="form.banka" placeholder="请输入银行名称" />
        </el-form-item>
        <el-form-item label="营销供应商标识" prop="zsfyxgys">
          <el-input v-model="form.zsfyxgys" placeholder="请输入营销供应商标识" />
        </el-form-item>
        <el-form-item label="营销类别" prop="zyxlb">
          <el-input v-model="form.zyxlb" placeholder="请输入营销类别" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSupplierMD, getSupplierMD, delSupplierMD, addSupplierMD, updateSupplierMD } from "@/api/mainData/SupplierMD";

export default {
  name: "SupplierMD",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商主数据表格数据
      SupplierMDList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partner: null,
        bkvid: null,
        nameOrg1: null,
        buSort1: null,
        buGroup: null,
        idnumber: null,
        country: null,
        langu: null,
        timeZone: null,
        steet: null,
        telNumber: null,
        mobNumber: null,
        smtpAddr: null,
        akont: null,
        loevm: null,
        banks: null,
        bankl: null,
        koinh: null,
        bankn: null,
        bkont: null,
        xezer: null,
        accname: null,
        efficient: null,
        updatetime: null,
        banka: null,
        zsfyxgys: null,
        zyxlb: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询供应商主数据列表 */
    getList() {
      this.loading = true;
      listSupplierMD(this.queryParams).then(response => {
        this.SupplierMDList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        sid: null,
        partner: null,
        bkvid: null,
        nameOrg1: null,
        buSort1: null,
        buGroup: null,
        idnumber: null,
        country: null,
        langu: null,
        timeZone: null,
        steet: null,
        telNumber: null,
        mobNumber: null,
        smtpAddr: null,
        akont: null,
        loevm: null,
        banks: null,
        bankl: null,
        koinh: null,
        bankn: null,
        bkont: null,
        xezer: null,
        accname: null,
        efficient: null,
        createTime: null,
        updatetime: null,
        banka: null,
        zsfyxgys: null,
        zyxlb: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sid)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加供应商主数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const sid = row.sid || this.ids
      getSupplierMD(sid).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改供应商主数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.sid != null) {
            updateSupplierMD(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSupplierMD(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const sids = row.sid || this.ids;
      this.$modal.confirm('是否确认删除供应商主数据编号为"' + sids + '"的数据项？').then(function() {
        return delSupplierMD(sids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mainData/SupplierMD/export', {
        ...this.queryParams
      }, `SupplierMD_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
