<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="150px">
      <el-form-item label="项目号">
        <el-input
          v-model="queryParams.projectNumber"
          placeholder="请输入项目号"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item label="IIT分类">
        <el-input
          v-model="queryParams.iitClass"
          placeholder="请输入IIT分类"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item label="立项部门">
        <el-input
          v-model="queryParams.setupDepartment"
          placeholder="请输入立项部门"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item label="费用承担部门">
        <el-input
          v-model="queryParams.chargeDepartment"
          placeholder="请输入费用承担部门"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item label="IIT中心">
        <el-input
          v-model="queryParams.iitCenter"
          placeholder="请输入IIT中心"
          clearable
          size="small"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item label="单据状态">
        <el-select
          v-model="queryParams.statusList"
          placeholder="请选择单据状态"
          clearable
          multiple
          collapse-tags
          size="small"
          style="width: 200px"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="tableData" border stripe>
      <el-table-column
        label="立项部门完整路径"
        align="center"
        prop="setupDepartmentFullPath"
        width="300"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="IIT分类"
        align="center"
        prop="iitClass"
        width="120"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="项目号"
        align="center"
        prop="projectNumber"
        width="150"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="领药系统单号"
        align="center"
        prop="receiveSysNumber"
        width="180"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="提交日期"
        align="center"
        prop="submitDate"
        width="120"
      />

      <el-table-column
        label="领药申请人"
        align="center"
        prop="submitter"
        width="120"
      />

      <el-table-column
        label="单据状态"
        align="center"
        prop="statusName"
        width="100"
      >
        <template slot-scope="scope">
          <el-tag
            :type="getStatusTagType(scope.row.status)"
            size="small"
          >
            {{ scope.row.statusName }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="费用承担部门完整路径"
        align="center"
        prop="chargeDepartmentFullPath"
        width="300"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="注射剂名称"
        align="center"
        prop="injectionName"
        width="150"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="总申请数量"
        align="center"
        prop="totalNum"
        width="120"
      />

      <el-table-column
        label="赠药数量"
        align="center"
        prop="injectionNum"
        width="120"
      />

      <el-table-column
        label="IIT中心"
        align="center"
        prop="iitCenter"
        width="150"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="科研科室"
        align="center"
        prop="researchDepartment"
        width="150"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="收货人"
        align="center"
        prop="receiver"
        width="120"
      />

      <el-table-column
        label="收货人联系电话"
        align="center"
        prop="receiverPhone"
        width="150"
      />

      <el-table-column
        label="收货地址"
        align="center"
        prop="receiveAddress"
        width="200"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="签收部门"
        align="center"
        prop="signDepartment"
        width="150"
        :show-overflow-tooltip="true"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { iitDonationReportPage, getStatusOptions } from '@/api/marketingFinance/IITDonationReport'

export default {
  name: 'IITDonationReport',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        projectNumber: null,
        iitClass: null,
        setupDepartment: null,
        chargeDepartment: null,
        iitCenter: null,
        statusList: [], // 单据状态列表（多选）
        pageSize: 10,
        pageNum: 1
      },
      // 表格数据
      tableData: [],
      // 单据状态选项
      statusOptions: [
        { code: 'DRAFT', name: '草稿' },
        { code: 'SUBMIT', name: '已提交' },
        { code: 'APPROVE', name: '已审批' },
        { code: 'REJECT', name: '已驳回' },
        { code: 'CANCEL', name: '已取消' },
        { code: 'CLOSE', name: '已关闭' }
      ],
      // 状态颜色映射
      statusColorMap: {
        'DRAFT': 'info',      // 灰色
        'SUBMIT': 'primary',  // 蓝色
        'APPROVE': 'success', // 绿色
        'REJECT': 'danger',   // 红色
        'CANCEL': 'warning',  // 橙色
        'CLOSE': 'info'       // 灰色
      }
    }
  },
  created() {
    this.getList()
    this.loadStatusOptions()
  },
  methods: {
    /** 查询IIT赠药报表列表 */
    getList() {
      this.loading = true

      // 构建查询参数
      const params = { ...this.queryParams }

      // 处理状态列表参数
      if (params.statusList && params.statusList.length === 0) {
        delete params.statusList
      }

      iitDonationReportPage(params).then(res => {
        this.tableData = res.data.rows || []
        if (this.queryParams.pageNum === 1) {
          this.total = res.data.total || 0
        }
        this.loading = false
      }).catch(error => {
        console.error('查询IIT赠药报表失败:', error)
        this.$modal.msgError('查询失败，请稍后重试')
        this.loading = false
      })
    },

    /** 重置查询条件 */
    resetQuery() {
      this.queryParams = {
        projectNumber: null,
        iitClass: null,
        setupDepartment: null,
        chargeDepartment: null,
        iitCenter: null,
        statusList: [],
        pageSize: 10,
        pageNum: 1
      }
      this.getList()
    },

    /** 获取状态标签类型 */
    getStatusTagType(status) {
      return this.statusColorMap[status] || 'info'
    },

    /** 导出按钮操作 */
    handleExport() {
      // 构建导出参数
      const params = { ...this.queryParams }

      // 处理状态列表参数
      if (params.statusList && params.statusList.length === 0) {
        delete params.statusList
      }

      // 移除分页参数
      delete params.pageNum
      delete params.pageSize

      this.$modal.confirm('是否确认导出所有IIT赠药报表数据项？').then(() => {
        this.loading = true
        return this.download('/marketingFinance/iitDonationReport/export', params, `IIT赠药报表_${new Date().getTime()}.xlsx`)
      }).then(() => {
        this.$modal.msgSuccess('导出成功')
      }).catch(error => {
        if (error !== 'cancel') {
          console.error('导出失败:', error)
          this.$modal.msgError('导出失败，请稍后重试')
        }
      }).finally(() => {
        this.loading = false
      })
    },

    /** 格式化部门路径显示 */
    formatDepartmentPath(path) {
      if (!path) return '-'
      // 如果路径太长，可以在这里进行截断处理
      return path.length > 50 ? path.substring(0, 50) + '...' : path
    },

    /** 加载状态选项 */
    loadStatusOptions() {
      getStatusOptions().then(res => {
        if (res.code === 200 && res.data) {
          this.statusOptions = res.data
        }
      }).catch(error => {
        console.warn('获取状态选项失败，使用默认选项:', error)
        // 保持使用默认的状态选项
      })
    },

    /** 验证查询参数 */
    validateQueryParams() {
      // 可以在这里添加查询参数的验证逻辑
      return true
    }
  }
}
</script>


<style scoped lang="scss">
.app-container {
  .el-form {
    .el-form-item {
      margin-bottom: 15px;
    }
  }

  .el-table {
    .el-table__header {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: bold;
      }
    }

    .el-table__body {
      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  // 状态标签样式
  .el-tag {
    &.el-tag--info {
      background-color: #f4f4f5;
      border-color: #e9e9eb;
      color: #909399;
    }

    &.el-tag--primary {
      background-color: #ecf5ff;
      border-color: #b3d8ff;
      color: #409eff;
    }

    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #b3e19d;
      color: #67c23a;
    }

    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }

    &.el-tag--warning {
      background-color: #fdf6ec;
      border-color: #f5dab1;
      color: #e6a23c;
    }
  }

  // 部门路径显示优化
  .department-path {
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      overflow: visible;
      white-space: normal;
      word-break: break-all;
    }
  }

  // 查询表单样式优化
  .el-form--inline {
    .el-form-item {
      margin-right: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }

      .el-input,
      .el-select {
        width: 200px;
      }
    }
  }

  // 工具栏样式
  .mb8 {
    margin-bottom: 8px;

    .el-button {
      margin-right: 10px;
    }
  }

  // 分页样式
  .pagination {
    text-align: center;
    margin-top: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    .el-form--inline {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: 15px;

        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }

    .el-table {
      font-size: 12px;

      .el-table-column {
        min-width: 100px;
      }
    }
  }
}
</style>


