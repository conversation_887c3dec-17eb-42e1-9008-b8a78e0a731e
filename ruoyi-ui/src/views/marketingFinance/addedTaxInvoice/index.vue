<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="发票号" prop="invoiceNo">
        <el-input
          v-model="queryParams.invoiceNo"
          placeholder="请输入发票号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商" prop="saleName">
        <el-input
          v-model="queryParams.saleName"
          placeholder="请输入供应商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用类型" prop="feeType">
        <el-input
          v-model="queryParams.feeType"
          placeholder="请输入费用类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单号" prop="invoiceNo">
        <el-input
          v-model="queryParams.oaNum"
          placeholder="请输入单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="daterangeInvoiceDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['marketingFinance:addedTaxInvoice:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="addedTaxInvoiceList" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="发票号" align="center" prop="invoiceNo" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="开票日期" align="center" prop="invoiceDate" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.invoiceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="税额" align="center" prop="totalTax" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="报销金额" align="center" prop="amountTax" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="费用类型" align="center" prop="feeType"  width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="发票内容" align="center" prop="invoiceContent" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="OA单号" align="center" prop="link" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <a target="_blank" style="color:#007bff;" v-if="scope.row.link!=null"
             v-bind:href="scope.row.link">
            {{ scope.row.oaNum }}
          </a>
          <span v-else> {{ scope.row.oaNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="OA单创建时间" align="center" prop="createDate" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="发票链接" align="center" prop="fileAddress" />
      <el-table-column label="供应商" align="center" prop="saleName" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listAddedTaxInvoice } from "@/api/marketingFinance/addedTaxInvoice";

export default {
  name: "AddedTaxInvoice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 增值税票表格数据
      addedTaxInvoiceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 开票日期时间范围
      daterangeInvoiceDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        invoiceNo: null,
        saleName: null,
        fileAddress: null,
        totalTax: null,
        amountTax: null,
        invoiceDate: null,
        feeType: null,
        invoiceContent: null,
        oaNum: null,
        createDate: null,
        link: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询增值税票列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeInvoiceDate && '' != this.daterangeInvoiceDate) {
        this.queryParams.params["beginCreateTime"] = this.daterangeInvoiceDate[0];
        this.queryParams.params["endCreateTime"] = this.daterangeInvoiceDate[1];
      }
      listAddedTaxInvoice(this.queryParams).then(response => {
        this.addedTaxInvoiceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        invoiceNo: null,
        fileAddress: null,
        totalTax: null,
        amountTax: null,
        invoiceDate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeInvoiceDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.invoiceNo)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('marketingFinance/addedTaxInvoice/export', {
        ...this.queryParams
      }, `addedTaxInvoice_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
