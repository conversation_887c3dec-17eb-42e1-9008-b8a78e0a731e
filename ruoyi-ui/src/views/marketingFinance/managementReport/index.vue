<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="申请人">
        <el-input v-model="queryParams.fullName" placeholder="请输入申请人名" clearable size="small"/>
      </el-form-item>
      <el-form-item label="部门">
        <el-input v-model="queryParams.department" placeholder="请输入所属部门" clearable size="small"/>
      </el-form-item>
      <el-form-item label="单据类型">
        <el-input v-model="queryParams.typeName" placeholder="请输入单据类型" clearable size="small"/>
      </el-form-item>
      <el-form-item label="单号">
        <el-input v-model="queryParams.documentNum" placeholder="请输入单号" clearable size="small"/>
      </el-form-item>
      <el-form-item label="费用承担部门">
        <el-input v-model="queryParams.chargeDepartmentName" placeholder="请输入所属部门" clearable size="small"/>
      </el-form-item>
      <el-form-item label="期间">
        <el-select v-model="queryParams.periodId" placeholder="请选择期间" clearable size="small">
          <el-option v-for="item in periods" :key="item.periodId" :value="item.periodId" :label="item.periodName"/>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 80px">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <!--        <el-tooltip class="item" effect="dark" content="导出" placement="top">-->
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
        <!--        </el-tooltip>-->
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="单号" align="center" prop="documentNum" width="200" :show-overflow-tooltip="true" fixed/>
      <el-table-column label="单据类型" align="center" prop="typeName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="提交日期" align="center" prop="submitDate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人" align="center" prop="fullName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="申请人工号" align="center" prop="employeeNumber" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="所属部门（末三级）" align="center" prop="submitDepartmentName3" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="所属部门（末二级）" align="center" prop="submitDepartmentName2" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="所属部门（末一级）" align="center" prop="submitDepartmentName" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="费用类型" align="center" prop="costType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="预算科目" align="center" prop="fbudgetName" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务类型" align="center" prop="businessType" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用承担部门（末三级）" align="center" prop="chargeDepartmentName3" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="费用承担部门（末二级）" align="center" prop="chargeDepartmentName2" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="费用承担部门（末级）" align="center" prop="chargeDepartmentName" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="费用说明" align="center" prop="description" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="项目号" align="center" prop="projectNumber" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="产品管线" align="center" prop="productPipeline" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="费用承担部门分摊金额" align="center" prop="apportionAmount" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="职级" align="center" prop="level" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="提交部门" align="center" prop="department" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="预算期间" align="center" prop="periodTime" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="业务审批时间" align="center" prop="closedDate" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="关闭月份" align="center" prop="endMouth" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="是否报销费用承担部门/预算科目与申请不一致" align="center" width="200"
                       :show-overflow-tooltip="true"
      >
        <template #default="{ row }">
          {{ row.sameDepartment ? '一致' : '不一致' }}
        </template>
      </el-table-column>
      <el-table-column label="核报单预算部门" align="center" prop="reportDepartment" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="核报单预算科目" align="center" prop="reportBudgetName" width="200"
                       :show-overflow-tooltip="true"
      />
      <el-table-column label="核报单金额" align="center" prop="reportAmount" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="部门差额" align="center" prop="sumBalance" width="200" :show-overflow-tooltip="true"
                       fixed="right"
      />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { managementReportPage, managementReportTotal } from '@/api/marketingFinance/managementReport'
import { periodList } from '@/api/marketingFinance/period'

export default {
  name: 'ManagementReporting',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        document: null,
        typeName: null,
        documentNum: null,
        periodId: null,
        chargeDepartmentName: null,
        pageSize: 10,
        pageNum: 1
      },
      tableData: [],
      periods: [{
        periodName: null,
        periodId: null
      }]
    }
  },
  created() {
    this.getList()
    this.getPeriod()
  },
  methods: {
    /** 查询目标终端列表 */
    getList() {
      this.loading = true
      managementReportPage(this.queryParams).then(res => {
        this.tableData = res.data.rows
        this.total = res.data.total
        // if (this.queryParams.pageNum === 1) {
        //   managementReportTotal(this.queryParams).then(res => {
        //     this.total = res.data
        //   })
        // }
        this.loading = false
      })
    },

    getPeriod() {
      periodList().then(res => {
        this.periods = res.data
      })
    },

    resetQuery() {
      this.queryParams = {
        document: null,
        typeName: null,
        documentNum: null,
        periodId: null,
        chargeDepartment: null,
        pageSize: 10,
        pageNum: 1
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/marketingFinance/managementReport/export', {
        ...this.queryParams
      }, `管报费用报表_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>


<style scoped lang="scss">

</style>


