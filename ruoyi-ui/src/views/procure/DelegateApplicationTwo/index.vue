<template>
  <div class="app-container">

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">

      <el-form-item label="姓名" prop="fdName">

        <el-input v-model="queryParams.fdName" placeholder="请输入姓名" clearable @keyup.enter.native="handleQuery"/>

      </el-form-item>

      <el-form-item label="部门" prop="fdName">

        <el-input v-model="queryParams.department" placeholder="请输入部门" clearable @keyup.enter.native="handleQuery"/>

      </el-form-item>

      <el-form-item label="服务名称" prop="fdName">

        <el-input v-model="queryParams.materialName" placeholder="请输入服务名称" clearable @keyup.enter.native="handleQuery"/>

      </el-form-item>

      <el-form-item label="文档标题" prop="applicationSubject">

        <el-input v-model="queryParams.applicationSubject" placeholder="请输入" clearable @keyup.enter.native="handleQuery"/>

      </el-form-item>

      <el-form-item label="供应商" prop="fdName">

        <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable
                  @keyup.enter.native="handleQuery"/>

      </el-form-item>

      <el-form-item label="单号" prop="">

        <el-input v-model="queryParams.serialno" placeholder="单号" clearable @keyup.enter.native="handleQuery"/>

      </el-form-item>

      <el-form-item label="状态" prop="">

        <el-select v-model="queryParams.status" multiple placeholder="请选择">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>

      </el-form-item>

      <el-form-item label="厂区" prop="">
        <el-select v-model="queryParams.factoryPlus" multiple placeholder="请选择">
          <el-option v-for="item in factoryOptions" :key="item.roleKey" :label="item.roleName" :value="item.roleKey">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="申请日期" label-width="90px">

        <el-date-picker ref="contractDatePicker" v-model="daterangeApplicationDate" style="width: 240px"
                        value-format="yyyy-MM-dd" type="daterange" unlink-panels range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"></el-date-picker>

      </el-form-item>


      <el-form-item style="margin-left: 80px">

        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>

        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>

      </el-form-item>

    </el-form>


<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-upload2"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--        >导出-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--    </el-row>-->


    <el-table v-loading="loading" border :max-height="tableMaxHeight" :data="dataList" :span-method="handleSpanMethod">

      <el-table-column label="OA采购申请单号" align="center" prop="serialno" key="serialno" v-if="columns[6].visible"
                       width="150" :show-overflow-tooltip='true'/>


      <el-table-column label="申请人" align="center" prop="fdName" key="fdName" width="150"
                       :show-overflow-tooltip='true'/>


      <el-table-column label="申请日期" align="center" prop="applicationDate" key="applicationDate" width="150"
                       :show-overflow-tooltip='true'>
        <!-- <template slot-scope="scope">
          <span>{{ parseTime(scope.row.workDate, '{y}-{m}-{d}') }}</span>
        </template> -->
      </el-table-column>

      <el-table-column label="部门" align="center" prop="department" key="department" :show-overflow-tooltip='true'/>

      <!-- <el-table-column label="物料名称" align="center" prop="materialName" key="materialName" width="150"
          :show-overflow-tooltip='true' /> -->

      <el-table-column label="文档标题" align="center" prop="applicationSubject" key="applicationSubject" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="流程状态" align="center" prop="documentStatus" key="documentStatus" width="150"
                       :show-overflow-tooltip='true'/>


      <el-table-column label="位置节点" align="center" prop="currentLink" key="currentLink" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="当前处理人" align="center" prop="currentHandler" key="currentHandler" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="服务代码" align="center" prop="materialCode" key="materialCode" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="服务名称" align="center" prop="materialName" key="materialName" width="150"
                       :show-overflow-tooltip='true'/>

      <!-- <el-table-column label="计量单位" align="center" prop="basicMeasurement" key="basicMeasurement" width="150"
          :show-overflow-tooltip='true' /> -->

      <el-table-column label="数量（项目次数）" align="center" prop="quantity" key="quantity" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="采购单位" align="center" prop="unit" key="unit" width="150" :show-overflow-tooltip='true'/>

      <el-table-column label="基本计量单位" align="center" prop="basicMeasurement" key="basicMeasurement" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="供应商代码" align="center" prop="vendorCode" key="vendorCode" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="供应商名称" align="center" prop="supplierName" key="supplierName" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="SAP生产订单号" align="center" prop="assetCode" key="assetCode" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="生产批次" align="center" prop="productionBatches" key="productionBatches" width="150"
                       :show-overflow-tooltip='true'/>

      <el-table-column label="预计完成时间" align="center" prop="deliveryDate" key="deliveryDate" width="150"
                       :show-overflow-tooltip='true'>
      </el-table-column>

      <el-table-column label="是否加急服务" align="center" prop="urgent" key="urgent" width="150"
                       :show-overflow-tooltip='true'/>
      <el-table-column fixed="right" label="操作"  width="100">
        <template slot-scope="scope">
          <a class="table-link" @click="handleClick(scope.row)">详情</a>
        </template>
      </el-table-column>
    </el-table>


    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>


  </div>
</template>


<script>

import {DelegateApplicationList, DelegateApplicationListTow, getRole,getCompany} from "@/api/procure/DelegateApplication";

export default {

  name: "DelegateApplication",

  data() {

    return {

      // 遮罩层

      loading: true,

      // 选中数组

      ids: [],

      // 非单个禁用

      single: true,

      // 非多个禁用

      multiple: true,

      // 显示搜索条件

      showSearch: true,

      spanArr: [],

      //时间范围

      daterangeApplicationDate: [],

      // 总条数

      total: 0,

      // 数据

      dataList: [],

      tableHeaders: [],

      // 弹出层标题

      title: "",

      // 是否显示弹出层

      open: false,
      // 康融东方，康方药业，康方赛诺，康方生物
      factoryOptions: [],
      statusOptions: [{
        value: '10',
        label: '草稿'
      }, {
        value: '20',
        label: '待审'
      }, {
        value: '11',
        label: '驳回'
      }, {
        value: '00',
        label: '废弃'
      }, {
        value: '30',
        label: '结束'
      }],

      // 查询参数

      queryParams: {

        pageNum: 1,

        pageSize: 10,

        queryDate: '',

        department: '',

        company: '',

        serialno: '',

        fdName: '',

        materialName: '',

        supplierName: '',

        applicationSubject: '',

        startDate: null,

        endDate: null,

        factoryPlus:[],

        factory: [],

        status: []

      },

      // 表单参数

      form: {},

      // 表单校验

      rules: {},

      // 列信息

      columns: [

        {key: 0, label: `姓名`, visible: true},

        {key: 1, label: `工号`, visible: true},

        {key: 2, label: `工作日期`, visible: true},

        {key: 3, label: `考勤工时`, visible: true},

        {key: 4, label: `报工工时`, visible: true},

        {key: 5, label: `报工项目号`, visible: true},

        {key: 6, label: `公司`, visible: true},

        {key: 7, label: `部门`, visible: true},

        {key: 8, label: `报工状态`, visible: true},

      ],

    };

  },

  created() {

    this.getList();
    this.getCompanys();
  },

  computed: {

    tableMaxHeight() {

      const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;

      return screenHeight - 230;

    }

  },

  methods: {
    handleClick(row) {
      console.log(row)
      // window.location.href = `http://oa.akesobio.com:9926/sys/modeling/main/modelingAppView.do?method=modelView&fdId=${row.fdId}&listviewId=17e0b75ff9b4bfefc8643d44739bd41f`,target=`_blank`;
      window.open(`http://oa.akesobio.com:9926/sys/modeling/main/modelingAppView.do?method=modelView&fdId=${row.fdId}&listviewId=17e0b75ff9b4bfefc8643d44739bd41f`, '_blank');
    },
    handleSpanMethod({

                       row, // 行

                       column, // 列

                       rowIndex, // 行索引

                       columnIndex, // 列索引

                     }) {

      if ([0, 1, 2, 3, 4, 5, 6].includes(columnIndex)) {

        /*

        表格数据：this.tableList

        判断合并行数：this.mergeColumn()

        */

        const _row = (this.mergeColumn(this.dataList).one)[rowIndex]

        const _col = _row > 0 ? 1 : 0

        return {

          rowspan: _row,

          colspan: _col

        }

      }

    },

    mergeColumn(data) {

      const spanOneArr = []

      let concatOne = 0

      data.forEach((item, index) => {

        if (index === 0) {

          spanOneArr.push(1)

        } else {

          //name 修改

          if (item.serialno === data[index - 1].serialno) { //第一列需合并相同内容的字段

            spanOneArr[concatOne] += 1

            spanOneArr.push(0)

          } else {

            spanOneArr.push(1)

            concatOne = index

          }

        }

      })

      return {

        one: spanOneArr

      }

    },
    getCompanys(){
      getCompany(this.$store.state.user.name,"entrust").then(response =>{
        this.factoryOptions = response.data;
        console.log(response.data)
      })
    },
    getList() {
      let factory = [];
      getRole(this.$store.state.user.name,"entrust").then(response => {
        this.factory = response.data;
        this.loading = true;
        this.queryParams.params = {};
        this.queryParams.factory = this.factory;

        if (null != this.daterangeApplicationDate && '' != this.daterangeApplicationDate) {
          this.queryParams.params["beginMealDate"] = this.daterangeApplicationDate[0];
          this.queryParams.params["endMealDate"] = this.daterangeApplicationDate[1];
        }

        if(this.queryParams && this.queryParams.factoryPlus && this.queryParams.factoryPlus.length > 0 ){
          this.queryParams.factory = [];
          this.queryParams.factory = this.queryParams.factoryPlus;
        }
        DelegateApplicationListTow(this.queryParams).then(response => {

          this.dataList = response.data.rows;
          this.total = response.data.total;
          this.loading = false;

        });
      });
    },

    // 取消按钮

    cancel() {

      this.open = false;

      this.reset();

    },

    /** 搜索按钮操作 */

    handleQuery() {

      this.queryParams.pageNum = 1;

      this.getList();

    },

    /** 重置按钮操作 */

    resetQuery() {

      this.queryParams = {};

      this.daterangeApplicationDate = [];

      this.resetForm("queryForm");

      this.handleQuery();

    },

    // 多选框选中数据

    handleSelectionChange(selection) {

      this.ids = selection.map(item => item.id)

      this.single = selection.length !== 1

      this.multiple = !selection.length

    },

    /** 导出按钮操作 */

    handleExport() {

      var date = new Date()

      var year = date.getFullYear().toString()

      var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()

      var day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()

      var h = date.getHours() < 10 ? '0' + date.getHours().toString() : date.getHours().toString()

      var m = date.getMinutes() < 10 ? '0' + date.getMinutes().toString() : date.getMinutes().toString()

      var s = date.getSeconds() < 10 ? '0' + date.getSeconds().toString() : date.getSeconds().toString()

      var dateTime = year + '-' + month + '-' + day + "_" + h + m + s

      this.download('procure/DelegateApplication/listTowExport', {

        ...this.queryParams

      }, `CG09${dateTime}.xlsx`)

    },

    watch: {

      // 监听日期清理后数据为null进行处理否则会报错

      'queryParams.queryDate'(newVal) {

        if (newVal == null) {

          this.queryParams.queryDate = ''

        }

      }

    }


  }

};

</script>

<style scoped lang="scss">
.table-link {
  color: #00c4ff;
  text-decoration: none;
  font-weight: bold;
  background-color: rgba(57, 216, 240, 0.33);
  border: 2px solid #fbbfbf;
  padding: 0px 10px;
  margin-right: 10px;
}

.table-link:hover {
  background-color: #902bc0;
  color: #fff;
}

.table-link {
  border-radius: 5px;
}

.table-link {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>

