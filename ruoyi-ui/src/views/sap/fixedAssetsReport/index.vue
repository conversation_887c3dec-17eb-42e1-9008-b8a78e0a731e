<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :rules="rules" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司代码" prop="bukrs" label-width="80px">
        <el-input
          v-model="queryParams.bukrs"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年度" prop="gjahr">
        <el-input
          v-model="queryParams.gjahr"
          placeholder="请输入会计年度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="期间" prop="monat">
        <el-input
          v-model="queryParams.monat"
          placeholder="请输入过账期间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSapQuery">SAP获取</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" type="flex" justify="end">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="导出" placement="top">
          <el-button
            type="primary"
            circle
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['sap:fixedAssetsReport:export']"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fixedAssetsReportList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公司代码" align="center" prop="bukrs" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="会计年度" align="center" prop="gjahr" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="过账期间" align="center" prop="monat" width="100" :show-overflow-tooltip="true"/>
      <el-table-column label="公司描述" align="center" prop="butxt" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产分类" align="center" prop="anlkl" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产分类描述" align="center" prop="txk20" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产编号" align="center" prop="anln1" fixed width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产主号文本" align="center" prop="anlhtxt" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产子编号" align="center" prop="anln2" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产描述1" align="center" prop="txt50" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="序列号" align="center" prop="sernr" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="库存编号" align="center" prop="invnr" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="库存注释" align="center" prop="invzu" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产描述2" align="center" prop="txa50" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="数量" align="center" prop="menge"width="200" :show-overflow-tooltip="true" />
      <el-table-column label="单位" align="center" prop="meins" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心" align="center" prop="kostl" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="成本中心描述" align="center" prop="ltext" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资本化日期" align="center" prop="aktiv" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="对象创建人姓名" align="center" prop="ernam" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="记录建立日期" align="center" prop="erdat" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="折旧码" align="center" prop="afasl" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="折旧范围描述" align="center" prop="afbktx" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="资产状态" align="center" prop="ordtx2" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="变动方式" align="center" prop="ordtx1" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="开始折旧时间" align="center" prop="afabg" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="使用寿命" align="center" prop="zndjar" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="已折旧期间数" align="center" prop="zyzjqjs" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="剩余使用期" align="center" prop="zsysyq" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="月折旧率(%)" align="center" prop="zyzjl" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="净残值率(%)" align="center" prop="schrwProz" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="净残值" align="center" prop="schrw" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="账面净值" align="center" prop="zjz" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="年初原值" align="center" prop="zncyz" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期末原值" align="center" prop="zqmyz" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="本月计提折旧额" align="center" prop="zbyljzj" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="本年累计折旧" align="center" prop="zbnljzj" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="年初累计折旧" align="center" prop="zncljzj" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期末数累计折旧" align="center" prop="zqmljzj" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="期末数减值准备" align="center" prop="zqmjzzb" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="冻结标识" align="center" prop="xspeb" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="删除标识" align="center" prop="xloev" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="不活动日期" align="center" prop="deakt" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="注销人" align="center" prop="aenam" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="对应折旧科目" align="center" prop="zzjkm" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="对应折旧科目描述" align="center" prop="zzjkmms" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="停用" align="center" prop="xstil" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="开始停用时间" align="center" prop="adatu" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="对应资产科目" align="center" prop="zzckm" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="对应资产科目描述" align="center" prop="zzckmms" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="对应费用科目" align="center" prop="zfykm" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="费用科目描述" align="center" prop="zfykmms" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商" align="center" prop="lifnr" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="供应商名称" align="center" prop="name1" width="200" :show-overflow-tooltip="true"/>
      <el-table-column label="制造商" align="center" prop="herst" width="200" :show-overflow-tooltip="true"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listFixedAssetsReport,
  getFixedAssetsReport,
  delFixedAssetsReport,
  addFixedAssetsReport,
  updateFixedAssetsReport,
  queryListZRPFI007
} from '@/api/sap/fixedAssetsReport'

export default {
  name: "FixedAssetsReport",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 固定资产清单报表表格数据
      fixedAssetsReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //防止插入重复
      submit: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bukrs: null,
        gjahr: null,
        monat: null,
        butxt: null,
        anlkl: null,
        txk20: null,
        anln1: null,
        anlhtxt: null,
        anln2: null,
        txt50: null,
        sernr: null,
        invnr: null,
        invzu: null,
        txa50: null,
        menge: null,
        meins: null,
        kostl: null,
        ltext: null,
        aktiv: null,
        ernam: null,
        erdat: null,
        afasl: null,
        afbktx: null,
        ordtx2: null,
        ordtx1: null,
        afabg: null,
        zndjar: null,
        zyzjqjs: null,
        zsysyq: null,
        zyzjl: null,
        schrwProz: null,
        schrw: null,
        zjz: null,
        zncyz: null,
        zqmyz: null,
        zbyljzj: null,
        zbnljzj: null,
        zncljzj: null,
        zqmljzj: null,
        zqmjzzb: null,
        xspeb: null,
        xloev: null,
        deakt: null,
        aenam: null,
        zzjkm: null,
        zzjkmms: null,
        xstil: null,
        adatu: null,
        zzckm: null,
        zzckmms: null,
        zfykm: null,
        zfykmms: null,
        lifnr: null,
        name1: null,
        herst: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        bukrs: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { min: 4, max: 4, message: '长度4个字符,如1050', trigger: 'blur' }
        ],
        gjahr: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { min: 4, max: 4, message: '长度4个字符,如2023', trigger: 'blur' }
        ],
        monat: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询固定资产清单报表列表 */
    getList() {
      this.loading = true;
      listFixedAssetsReport(this.queryParams).then(response => {
        this.fixedAssetsReportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        sid: null,
        bukrs: null,
        gjahr: null,
        monat: null,
        butxt: null,
        anlkl: null,
        txk20: null,
        anln1: null,
        anlhtxt: null,
        anln2: null,
        txt50: null,
        sernr: null,
        invnr: null,
        invzu: null,
        txa50: null,
        menge: null,
        meins: null,
        kostl: null,
        ltext: null,
        aktiv: null,
        ernam: null,
        erdat: null,
        afasl: null,
        afbktx: null,
        ordtx2: null,
        ordtx1: null,
        afabg: null,
        zndjar: null,
        zyzjqjs: null,
        zsysyq: null,
        zyzjl: null,
        schrwProz: null,
        schrw: null,
        zjz: null,
        zncyz: null,
        zqmyz: null,
        zbyljzj: null,
        zbnljzj: null,
        zncljzj: null,
        zqmljzj: null,
        zqmjzzb: null,
        xspeb: null,
        xloev: null,
        deakt: null,
        aenam: null,
        zzjkm: null,
        zzjkmms: null,
        xstil: null,
        adatu: null,
        zzckm: null,
        zzckmms: null,
        zfykm: null,
        zfykmms: null,
        lifnr: null,
        name1: null,
        herst: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** SAP搜索按钮操作 */
    handleSapQuery() {
      this.$refs.queryForm.validate((valid) => {
        if (valid){
          this.queryParams.pageNum = 1;
          this.getQueryList();
        } else {
          this.$message('搜索项未填写成功');
        }
      })
      this.getList();
    },
    async getQueryList() {
      if (this.submit) {
        this.submit = false;
        await queryListZRPFI007(this.queryParams.bukrs, this.queryParams.gjahr, this.queryParams.monat).then(response => {
          this.submit = true;
        })
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.reset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sid)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sap/fixedAssetsReport/export', {
        ...this.queryParams
      }, `fixedAssetsReport_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
